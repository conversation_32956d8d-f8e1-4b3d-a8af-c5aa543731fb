import { IReward } from '../interfaces'

export const parseCustomerRewards = (data: any[]): IReward[] => {
  if (!Array.isArray(data)) return []
  
  return data.map(reward => ({
    id: reward.id || '',
    rewardType: reward.reward_type || reward.rewardType || '',
    pointsEarned: reward.points_earned || reward.pointsEarned || 0,
    pointsRedeemed: reward.points_redeemed || reward.pointsRedeemed || 0,
    pointsBalance: reward.points_balance || reward.pointsBalance || 0,
    rewardValue: reward.reward_value || reward.rewardValue || 0,
    expirationDate: reward.expiration_date || reward.expirationDate || '',
    status: reward.status || '',
    createdDate: reward.created_date || reward.createdDate || '',
    lastModifiedDate: reward.last_modified_date || reward.lastModifiedDate || '',
  }))
}
