import { IDocument } from '../interfaces'

export const parseCustomerDocuments = (data: any[]): IDocument[] => {
  if (!Array.isArray(data)) return []
  
  return data.map(document => ({
    id: document.id || '',
    documentName: document.document_name || document.documentName || '',
    documentType: document.document_type || document.documentType || '',
    fileSize: document.file_size || document.fileSize || 0,
    uploadedBy: document.uploaded_by || document.uploadedBy || '',
    uploadedDate: document.uploaded_date || document.uploadedDate || '',
    lastModifiedDate: document.last_modified_date || document.lastModifiedDate || '',
    downloadUrl: document.download_url || document.downloadUrl || '',
    isActive: document.is_active || document.isActive || true,
    category: document.category || '',
    description: document.description || '',
  }))
}
