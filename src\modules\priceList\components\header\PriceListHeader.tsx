/* eslint-disable react-hooks/exhaustive-deps */
import {useContext, useEffect, useState} from 'react'
import {PriceListContext} from '../../context'
import Search from '../../../../components/Search'
import {useGetProducts} from '../../hooks/useGetProducts'
import {ReachAsyncSelect} from '../../../../components/reachAsyncSelect'
import {CSVImporter} from '../../../../components/CSVImporter'
import {SampleCSVDownload} from '../../../../components/SampleCSVDownload'
import {
  priceListCsvConfig,
  samplePriceListCSVData,
  transformPriceListData,
  updateDefaultColumnsWithMonths,
} from '../../utils'
import {ConfirmationModal} from '../../../../components/ConfirmationModal'
import {usePriceListOperations} from '../../hooks/usePriceListOperations'
import {Select} from '../../../../components/Select/'
import MultipleSelect from '../../../../components/MultiSelect/MultiSelect'
import {TableSettingsMenu} from '../../../../components/tableSettingsMenu'
import {useAppSelector} from '../../../../redux/useTypedSelector'
import usePermission from '../../../../hook/usePermission'
import useGetJobData from '../../../jobs/hooks/useGetJobData'

export default function PriceListHeader() {
  const {
    onSearch,
    Tags,
    onApplyFilter,
    PriceListMeta,
    Classifications,
    filters,
    ClassifiedAs,
    createPriceList,
    isOperationLoading,
    PriceListData,
    refetch,
    Suppliers,
    onUserFilterChange,
    FilterUsers,
    FilterUsersLoading,
    ClassificationsLoading,
    downloadPriceListCSV,
    isDownloading,
    months,
    globalDefaultColumnsList,
    isLoadingGlobalDefaultColumnsList,
    refetchGlobalDefaultColumnsList,
    updateGlobalDefaultColumnsTableSorting,
    isLoadingGlobalDefaultColumnOperation,
  } = useContext(PriceListContext)
  const {user} = useAppSelector((state) => state.auth)
  const [selectedTags, setSelectedTags] = useState([])
  const [selectedClassification, setSelectedClassification] = useState([])
  const [selectedClassifiedAsValue, setSelectedClassifiedAsValue] = useState([])
  const [selectedProducts, setSelectedProducts] = useState([])
  const [selectedUser, setSelectedUser] = useState<any>('')
  const [selectedTopProducts, setSelectedTopProducts] = useState('')
  const [selectedPercentage, setSelectedPercentage] = useState('')
  const [selectedPrimarySuppliers, setSelectedPrimarySuppliers] = useState<any[]>([])
  const [errors, setErrors] = useState<string[] | null>(null)
  const {checkUpdatedStatus: priceListCheckUpdatedStatus} = usePriceListOperations()
  const [isUploading, setIsUploading] = useState(false)
  const [showSuccessModal, setShowSuccessModal] = useState(false)
  const [showModal, setShowModal] = useState(false)
  const [message, setMessage] = useState('')
  const [isResetProductsOptions, setIsResetProductsOptions] = useState(false)
  const [multiSelectIds, setMultiSelectIds] = useState({
    primarySupplier: Date.now(),
    classification: Date.now(),
    tags: Date.now(),
    classifiedAs: Date.now(),
  })
  const [isOpen, setIsOpen] = useState(false)
  const {hasPermission} = usePermission()
  const isPermission = hasPermission('products_price list', 'write')
  const defaultColumns = updateDefaultColumnsWithMonths(months, PriceListMeta, user?.role_id)
  const {jobData, runTask, refetchJobData} = useGetJobData(
    'sync_skuvault_and_price_lists_data'
  )

  const defaultData = transformPriceListData(PriceListData)

  const isAnyFilterApplied =
    filters.tag_filter ||
    filters.classification_filter ||
    filters.classified_as_filter ||
    filters.user_filter ||
    filters.supplier_filter ||
    filters.top_products_filter ||
    filters.products_filter ||
    filters.cost_margin_filter

  useEffect(() => {
    let intervalId: any = null
    if (jobData?.status === 'running') {
      intervalId = setInterval(() => {
        refetchJobData()
      }, 30000)
    }
    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jobData?.status === 'running', refetchJobData])

  useEffect(() => {
    if (isOperationLoading) {
      let intervalId: NodeJS.Timeout | null = null
      let timeoutId: NodeJS.Timeout | null = null

      const checkStatus = async () => {
        try {
          const res: any = await priceListCheckUpdatedStatus()

          if (res?.data === 'running') {
            // Start polling if not already started
            if (!intervalId) {
              intervalId = setInterval(checkStatus, 30000)
            }
          } else if (res?.data === 'completed') {
            if (intervalId) {
              clearInterval(intervalId)
              intervalId = null
            }
            setIsUploading(false)
            refetch()
          }
        } catch (error) {
          if (intervalId) {
            clearInterval(intervalId)
            intervalId = null
          }
        }
      }

      // Delay initial call
      timeoutId = setTimeout(checkStatus, 500) // initial 2s delay

      // Cleanup on unmount or change
      return () => {
        if (intervalId) clearInterval(intervalId)
        if (timeoutId) clearTimeout(timeoutId)
      }
    }
  }, [isOperationLoading])

  const {
    ProductsData,
    isLoading: isLoadingProduct,
    pagination,
    onPageChange,
    onSearch: onProductSearch,
  } = useGetProducts()

  const handleMultiSelectTagsChange = (selectedOptions: any) => {
    setSelectedTags(selectedOptions)
  }

  const handleMultiSelectClassificationAsChange = (selectedOptions: any) => {
    setSelectedClassifiedAsValue(selectedOptions)
  }

  const handleMultiSelectClassificationChange = (selectedOptions: any) => {
    setSelectedClassification(selectedOptions)
  }

  const handleMultiSelectProductChange = (selectedOptions: any) => {
    setSelectedProducts(selectedOptions)
  }

  const handleTopProductsChange = (value: string) => {
    setSelectedTopProducts(value)
  }

  const handlePercentageChange = (value: any) => {
    setSelectedPercentage(value)
  }

  const handleApplyFilters = async () => {
    setIsResetProductsOptions(true)
    const newSelectedTags = selectedTags.map((option: any) => option.value).join(',')
    const newSelectedClassifiedAsValue = selectedClassifiedAsValue
      .map((option: any) => option.value)
      .join(',')
    const newSelectedClassification = selectedClassification
      .map((option: any) => option.value)
      .join(',')
    let newSelectedProducts = selectedProducts.map((option: any) => option.value).join(',')
    const selectedUserValue = selectedUser || ''
    const selectedSupplierValues = selectedPrimarySuppliers
      .map((option: any) => option.value)
      .join(';')
    const selectedTopProductsValue = selectedTopProducts || ''

    await onApplyFilter(
      newSelectedTags,
      newSelectedClassifiedAsValue,
      newSelectedClassification,
      newSelectedProducts,
      selectedUserValue,
      selectedSupplierValues,
      selectedTopProductsValue,
      selectedPercentage
    )
    setIsResetProductsOptions(false)
  }

  const handleResetFilters = () => {
    setIsResetProductsOptions(true)
    onApplyFilter('', '', '', '', '', '', ``, null)
    setSelectedTags([])
    setSelectedClassifiedAsValue([])
    setSelectedClassification([])
    setSelectedUser('') // Ensure null instead of empty string if needed
    setSelectedPrimarySuppliers([])
    setSelectedProducts([])
    setSelectedTopProducts('')
    setSelectedPercentage('')

    setMultiSelectIds({
      primarySupplier: Date.now(),
      classification: Date.now(),
      tags: Date.now(),
      classifiedAs: Date.now(),
    })
    setIsResetProductsOptions(false)
  }

  const handleUploadSuccess = async (data: any, name: any) => {
    try {
      setIsUploading(true)

      // Remove "Product name" from each row
      const cleanedData = data.map((row: any) => {
        const {'Product name': _, ...rest} = row
        return rest
      })

      const payload = {
        data: cleanedData,
        filename: name,
      }

      const res: any = await createPriceList(payload)

      if (res?.status !== 200) {
        setErrors(res?.error)
        setIsUploading(false)
        return
      }

      setShowSuccessModal(true)
    } catch (error) {
      console.error('Upload error:', error)
      setIsUploading(false)
    }
  }

  const handleDownLoadCSV = async () => {
    const res: any = await downloadPriceListCSV(filters)
    if (res?.data?.message !== '' && res?.status === 200) {
      setShowModal(true)
      setMessage(res?.data?.message)
    }
  }

  const handleUserChange = (selectedValue: any) => {
    setSelectedUser(selectedValue)
    onUserFilterChange(selectedValue)
    setSelectedClassification([])
    setSelectedPrimarySuppliers([])
    setSelectedProducts([])
  }

  const onClose = () => {
    setIsOpen(false)
  }

  return (
    <div className='mb-10'>
      <div className='d-flex justify-content-between mb-7'>
        <div className='d-flex'>
          <Search
            onSearch={(searchTerm: string) => {
              onSearch && onSearch(searchTerm)
            }}
          />
        </div>
        <div className='d-flex'>
          {hasPermission('settings_jobs', 'write') && (
            <div className='position-relative d-flex align-items-center me-5'>
              <button
                onClick={() => runTask('sync_skuvault_and_price_lists_data')}
                className='btn btn-outline'
              >
                <div className='d-flex align-items-center'>
                  {jobData?.status === 'running' ? (
                    <span className='spinner-border spinner-border-sm align-middle me-2'></span>
                  ) : (
                    <i className='fas fa-sync cursor-pointer fs-5 me-2'></i>
                  )}
                  {jobData?.status === 'running' ? 'Syncing...' : 'Sync Data'}
                </div>
              </button>
            </div>
          )}
          {isPermission && (
            <>
              <button
                className='btn'
                onClick={() => SampleCSVDownload(samplePriceListCSVData, 'Sample Price List')}
              >
                <span className='text-decoration-underline'>Download Sample CSV</span>
              </button>
              <span className='me-5'>
                <CSVImporter
                  id={'replenishment-back-order'}
                  config={priceListCsvConfig}
                  existingData={defaultData}
                  onSuccess={handleUploadSuccess}
                  isLoading={isUploading}
                />
              </span>
            </>
          )}
          <TableSettingsMenu
            id='kt_drawer_product_price_list'
            defaultColumns={defaultColumns}
            permissionPath=''
            refetch={refetch}
            dragDisable={true}
            type={'product_pricelist'}
            globalDefaultColumnsList={globalDefaultColumnsList}
            isLoadingGlobalDefaultColumnsList={isLoadingGlobalDefaultColumnsList}
            refetchGlobalDefaultColumnsList={refetchGlobalDefaultColumnsList}
            updateGlobalDefaultColumnsTableSorting={updateGlobalDefaultColumnsTableSorting}
            isLoadingGlobalDefaultColumnOperation={isLoadingGlobalDefaultColumnOperation}
            isDrawerOpen={isOpen}
            closeDrawer={onClose}
            setIsDrawerOpen={setIsOpen}
          />
        </div>
      </div>

      <div className='bottom-filter-section d-flex align-items-center-1 mb-5 row'>
        <div className={`w-25`}>
          <label className='form-label  me-3 mb-2'>Select User</label>
          <Select
            name='user'
            value={selectedUser}
            placeholder='Select User'
            options={FilterUsers}
            labelKey='name'
            valueKey='name'
            onChange={(e: any) => {
              const selectedValue = e.target.value
              handleUserChange(selectedValue)
            }}
            isLoading={FilterUsersLoading}
            id={'select-user'}
          />
        </div>

        <div className={`w-25`}>
          <label className='form-label me-3 mb-2'>Primary Supplier</label>
          <MultipleSelect
            id={`primary-supplier-${multiSelectIds.primarySupplier}`}
            options={Suppliers}
            handleSelected={(selected) => {
              setSelectedPrimarySuppliers(selected)
            }}
            placeholder='Select Primary Supplier'
            isLoading={ClassificationsLoading}
            selectedValues={selectedPrimarySuppliers}
          />
        </div>

        <div className={'w-25'}>
          <label className={'fw-semibold me-3 mb-2'}>{'Classifications'}</label>
          <MultipleSelect
            id={`classification-${multiSelectIds.classification}`}
            options={Classifications}
            handleSelected={handleMultiSelectClassificationChange}
            placeholder='Select Classification'
            isLoading={ClassificationsLoading}
            selectedValues={selectedClassification}
          />
        </div>

        <div className={'w-25'}>
          <h5 className='fw-semibold me-3 mb-2'>Tags</h5>
          <MultipleSelect
            id={`tags-${multiSelectIds.tags}`}
            options={Tags}
            handleSelected={handleMultiSelectTagsChange}
            defaultValues={selectedTags}
            placeholder='Select Tags'
            className='text-uppercase'
          />
        </div>

        <div className={'w-25 mt-5'}>
          <h5 className='fw-semibold me-3 mb-2'>Classified as</h5>
          <MultipleSelect
            id={`classified-as-${multiSelectIds.classifiedAs}`}
            options={ClassifiedAs}
            handleSelected={handleMultiSelectClassificationAsChange}
            placeholder='Select Classified As'
          />
        </div>

        <div className='w-25 mt-5'>
          <ReachAsyncSelect
            id={'price-list-product'}
            label='Products'
            placeholder='Select Products'
            options={ProductsData}
            value={selectedProducts}
            disabledKey='disabled'
            isNullable={true}
            isLoading={isLoadingProduct}
            onChange={handleMultiSelectProductChange}
            onSearch={onProductSearch}
            selectClass={'react-select-multi-tags'}
            pagination={pagination}
            onPageChange={onPageChange}
            isMultiSelect={true}
            isResetOptions={isResetProductsOptions}
          />
        </div>

        <div className='w-25 mt-5'>
          <Select
            name='top_products'
            value={selectedTopProducts}
            placeholder='Select Top Products'
            label='Top Products'
            options={[
              {label: 'Top 10 Products', value: 10},
              {label: 'Top 50 Products', value: 50},
              {label: 'Top 100 Products', value: 100},
              {label: 'Top 200 Products', value: 200},
              {label: 'Top 500 Products', value: 500},
            ]}
            labelKey='label'
            valueKey='value'
            onChange={(e: any) => {
              const selectedValue = e.target.value
              handleTopProductsChange(selectedValue)
            }}
            isLoading={FilterUsersLoading}
            id={'select-top-products'}
          />
        </div>

        <div className='w-25 mt-5'>
          <Select
            name='cost-percentage'
            value={selectedPercentage}
            placeholder='Select Cost Margin'
            label='Cost Margin%'
            options={[
              {label: '+20%', value: 20},
              {label: '+15%', value: 15},
              {label: '+10%', value: 10},
              {label: '+5%', value: 5},
              {label: '0%', value: 0},
              {label: '-5%', value: -5},
              {label: '-10%', value: -10},
              {label: '-15%', value: -15},
              {label: '-20%', value: -20},
            ]}
            labelKey='label'
            valueKey='value'
            valueType={'number'}
            onChange={(e: any) => {
              const selectedValue = e.target.value
              handlePercentageChange(selectedValue)
            }}
            isLoading={FilterUsersLoading}
            id={'select-top-products'}
          />
        </div>
      </div>
      {errors && Array.isArray(errors) && (
        <ConfirmationModal
          show={errors ? true : false}
          onClose={() => setErrors(null)}
          title={'CSV Import Error!'}
          body={
            <ul>
              {errors.map((error: any, index: number) => (
                <li key={index} className='mb-3'>
                  <strong> {error?.message}</strong> : {error?.key}
                </li>
              ))}
            </ul>
          }
          onAction={() => setErrors(null)}
          disableAction={true}
        />
      )}
      <div className='d-flex mt-7 gap-5'>
        <button
          type='button'
          className='btn btn-outline'
          onClick={handleResetFilters}
          disabled={!isAnyFilterApplied}
        >
          Reset Filters
        </button>
        <button className='btn btn-primary ' onClick={handleApplyFilters}>
          Apply Filters
        </button>
        {(user?.is_owner ||
          user?.username === '<EMAIL>' ||
          user?.username === '<EMAIL>') && (
          <button
            className='btn btn-primary'
            onClick={handleDownLoadCSV}
            disabled={isDownloading || !defaultData?.length}
          >
            <div className='d-flex align-items-center'>
              {isDownloading ? 'Downloading...' : 'Download CSV'}
              {isDownloading && (
                <span className='spinner-border spinner-border-sm align-middle ms-2'></span>
              )}
            </div>
          </button>
        )}
      </div>

      {/* Success Modal */}
      <ConfirmationModal
        show={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title='CSV Import Success'
        body={
          <div>
            CSV validated successfully. Price update process has started. You will receive an email
            once it's completed.
          </div>
        }
      />

      <ConfirmationModal
        show={showModal}
        onClose={() => {
          setShowModal(false)
          setMessage('')
        }}
        title={'Generating Your CSV File'}
        disableAction={true}
        onAction={() => {
          setShowModal(false)
          setMessage('')
        }}
        okayBtnClass={'btn-primary'}
        body={
          <div>
            <p>We are generating the data you requested for download as a CSV file.</p>
            <p>
              This process may take a few minutes. Once complete, the CSV will be sent to your
              email: <b>{message.split(':')[1]}</b>
            </p>
          </div>
        }
      />
    </div>
  )
}
