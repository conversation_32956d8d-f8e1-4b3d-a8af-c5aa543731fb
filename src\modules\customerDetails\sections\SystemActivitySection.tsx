import React from 'react'
import {KTSVG} from '../../../_metronic/helpers'

const InfoField = ({icon, label, value}: {icon?: string; label: string; value: string}) => (
  <div className='d-flex align-items-center mb-3'>
    {icon && (
      <div className='me-3'>
        <KTSVG path={icon} className='svg-icon-5 text-muted' />
      </div>
    )}
    <div className='flex-grow-1'>
      <span className='text-gray-700 fs-6'>{label}:</span>
      <span className='text-gray-800 fw-semibold ms-2'>{value}</span>
    </div>
  </div>
)

const ActivityItem = ({
  icon,
  iconColor,
  title,
  subtitle,
  time,
}: {
  icon: string
  iconColor: string
  title: string
  subtitle: string
  time: string
}) => (
  <div className='d-flex align-items-start mb-4'>
    <div className={`symbol symbol-35px bg-light-${iconColor} me-4`}>
      <div className='symbol-label'>
        <KTSVG path={icon} className={`svg-icon-2 text-${iconColor}`} />
      </div>
    </div>
    <div className='flex-grow-1'>
      <div className='fw-bold text-gray-800 mb-1'>{title}</div>
      <div className='text-muted fs-7'>{subtitle}</div>
    </div>
    <div className='text-muted fs-7'>{time}</div>
  </div>
)

const SystemActivitySection = () => (
  <div className='row g-5 mb-5'>
    {/* System Information */}
    <div className='col-12'>
      <div className='card border-0 bg-light'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center mb-5'>
            <KTSVG
              path='/media/icons/duotune/general/gen014.svg'
              className='svg-icon-2 text-primary me-2'
            />
            <h4 className='fw-bold m-0 text-gray-800'>System Information</h4>
          </div>
          <div className='row'>
            <div className='col-md-6'>
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='Attempt1 date capture'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/communication/com006.svg'
                label='Created By'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/communication/com006.svg'
                label='Last Modified By'
                value='Not Set'
              />
            </div>
            <div className='col-md-6'>
              <InfoField
                icon='/media/icons/duotune/communication/com006.svg'
                label='User Who last modified Notes'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='Notes Last Modified Date/Time'
                value='Not Set'
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Activity & Notes */}
    <div className='col-12'>
      <div className='card border-0 bg-light'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center mb-5'>
            <KTSVG
              path='/media/icons/duotune/general/gen007.svg'
              className='svg-icon-2 text-primary me-2'
            />
            <h4 className='fw-bold m-0 text-gray-800'>Activity & Notes</h4>
          </div>

          {/* Activity Timeline */}
          <div className='mb-8'>
            <h5 className='fw-bold mb-4 text-gray-800'>Activity Timeline</h5>
            <div className='timeline'>
              <div className='d-flex align-items-center mb-4'>
                <div className='symbol symbol-35px bg-success me-4'>
                  <div className='symbol-label'>
                    <KTSVG
                      path='/media/icons/duotune/ecommerce/ecm002.svg'
                      className='svg-icon-3 text-white'
                    />
                  </div>
                </div>
                <div className='flex-grow-1'>
                  <div className='text-gray-800 fw-semibold fs-6'>
                    Order #ORD-003 placed for $2,850
                  </div>
                  <div className='text-muted fs-7'>by System</div>
                </div>
                <div className='text-muted fs-7'>10/01/2024 at 16:00:00</div>
              </div>

              <div className='d-flex align-items-center mb-4'>
                <div className='symbol symbol-35px bg-warning me-4'>
                  <div className='symbol-label'>
                    <KTSVG
                      path='/media/icons/duotune/communication/com005.svg'
                      className='svg-icon-3 text-white'
                    />
                  </div>
                </div>
                <div className='flex-grow-1'>
                  <div className='text-gray-800 fw-semibold fs-6'>
                    Phone call regarding upcoming order
                  </div>
                  <div className='text-muted fs-7'>by Sarah Johnson</div>
                </div>
                <div className='text-muted fs-7'>08/01/2024 at 19:45:00</div>
              </div>
            </div>
          </div>

          {/* Customer Notes */}
          <div>
            <h5 className='fw-bold mb-4 text-gray-800'>Customer Notes</h5>
            <div className='mb-4'>
              <div className='d-flex align-items-center mb-2'>
                <div className='text-gray-800 fw-bold fs-6'>Sarah Johnson</div>
                <div className='text-muted fs-7 ms-auto'>05/01/2024, 14:30:00</div>
              </div>
              <div className='text-gray-700 fs-6'>
                Customer interested in bulk pricing for Q2 orders
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
)

export default SystemActivitySection
