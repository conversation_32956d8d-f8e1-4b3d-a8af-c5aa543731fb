import React from 'react'
import { getBadgeColor } from '../../../utils/badge'

const PageTitleComponent = ({customerDetails, customerId}:any) => {
  return (
    <div className='d-flex gap-3 align-items-center'>
      <div className='symbol symbol-50px bg-primary bg-opacity-10 rounded-2'>
        <div className='symbol-label'>
          <i className='las la-building fs-1 text-primary'></i>
        </div>
      </div>
      <div>
        <div className='text-dark fw-bold fs-4 mb-2'>{customerDetails.customerName}</div>
        <div className='d-flex gap-2 fs-8 align-items-center'>
          <span>
            {customerDetails.status ? (
              <span
                className={`badge ${getBadgeColor(
                  customerDetails.status,
                  'light'
                )} badge-lg text-capitalize`}
              >
                {customerDetails.status}
              </span>
            ) : (
              '-'
            )}
          </span>
          <span>ID: {customerId}</span>
        </div>
      </div>
    </div>
  )
}

export default PageTitleComponent
