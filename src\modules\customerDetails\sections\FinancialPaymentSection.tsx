import React, {useContext} from 'react'
import {KTSVG} from '../../../_metronic/helpers'
import {FinancialPaymentContext} from '../context'

const InfoField = ({
  icon,
  label,
  value,
  className = '',
}: {
  icon?: string
  label: string
  value?: string
  className?: string
}) => (
  <div className={`d-flex align-items-center mb-3 ${className}`}>
    {icon && (
      <div className='me-3'>
        <KTSVG path={icon} className='svg-icon-5 text-muted' />
      </div>
    )}
    <div className='flex-grow-1'>
      <span className='text-gray-700 fs-6'>{label}:</span>
      <span className='text-gray-800 fw-semibold ms-2'>{value ?? '-'} </span>
    </div>
  </div>
)

const FinancialPaymentSection = () => {
  const {financialPayment, isLoading} = useContext(FinancialPaymentContext)
  if (!financialPayment || isLoading) {
    return <div className='text-center py-10'>Loading...</div>
  }
  const {paymentAndCredit, cashAndCarry, creditCard, ach, netTerms} = financialPayment

  return (
    <div className='row g-5 mb-5'>
      {/* Payment & Credit */}
      <div className='col-12'>
        <div className='card border'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-5'>
              <KTSVG
                path='/media/icons/duotune/finance/fin010.svg'
                className='svg-icon-2 text-primary me-2'
              />
              <h4 className='fw-bold m-0 text-gray-800'>Payment & Credit</h4>
            </div>
            <div className='row'>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/finance/fin006.svg'
                  label='Payment Term'
                  value={paymentAndCredit.paymentTerm}
                />
                <InfoField
                  icon='/media/icons/duotune/finance/fin010.svg'
                  label='Payment Term Notes'
                  value={paymentAndCredit.paymentTermNotes}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen014.svg'
                  label='Tax Exempt'
                  value={paymentAndCredit.taxExempt}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/finance/fin010.svg'
                  label='Credit Limit'
                  value={paymentAndCredit.creditLimit}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen043.svg'
                  label='Payment Term Approved By'
                  value={paymentAndCredit.paymentTermApprovedBy}
                />
                <InfoField
                  icon='/media/icons/duotune/finance/fin006.svg'
                  label='Currency'
                  value={paymentAndCredit.currency}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/finance/fin006.svg'
                  label='Unpaid Balance'
                  value={paymentAndCredit.unpaidBalance}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen014.svg'
                  label='Payment Term Approved Date & Time'
                  value={paymentAndCredit.paymentTermApprovedDateTime}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen043.svg'
                  label='Payment Term Risk'
                  value={paymentAndCredit.paymentTermRisk}
                />
              </div>
            </div>
            <div className='row mt-4'>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/finance/fin006.svg'
                  label='Outstanding Balance'
                  value={paymentAndCredit.outstandingBalance}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/general/gen043.svg'
                  label='Payment Term Issues'
                  value={paymentAndCredit.paymentTermIssues}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Cash & Carry */}
      <div className='col-12'>
        <div className='card border'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-5'>
              <KTSVG
                path='/media/icons/duotune/finance/fin006.svg'
                className='svg-icon-2 text-warning me-2'
              />
              <h4 className='fw-bold m-0 text-gray-800'>Cash & Carry</h4>
            </div>
            <div className='row'>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/general/gen043.svg'
                  label='Cash & Carry Check Approved?'
                  value={cashAndCarry.cashAndCarryCheckApproved}
                />
                <InfoField
                  icon='/media/icons/duotune/finance/fin006.svg'
                  label='Check Approved Amount Limit'
                  value={cashAndCarry.checkApprovedAmountLimit}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/communication/com006.svg'
                  label='Check Approved By'
                  value={cashAndCarry.checkApprovedBy}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen043.svg'
                  label='Noted for Check'
                  value={cashAndCarry.notedForCheck}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/general/gen014.svg'
                  label='Check Approved Date & Time'
                  value={cashAndCarry.checkApprovedDateTime}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen043.svg'
                  label='Does The Customer have account?'
                  value={cashAndCarry.doesTheCustomerHaveAccount}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Credit Card */}
      <div className='col-12'>
        <div className='card border'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-5'>
              <KTSVG
                path='/media/icons/duotune/finance/fin002.svg'
                className='svg-icon-2 text-success me-2'
              />
              <h4 className='fw-bold m-0 text-gray-800'>Credit Card</h4>
            </div>
            <div className='row'>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/general/gen043.svg'
                  label='Customer Credit Card Fees Exempt'
                  value={creditCard.customerCreditCardFeesExempt}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/communication/com006.svg'
                  label='Credit Card Fees Exempt Approved By'
                  value={creditCard.creditCardFeesExemptApprovedBy}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/general/gen014.svg'
                  label='Credit Card Fees Exempt Approved Date'
                  value={creditCard.creditCardFeesExemptApprovedDate}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* ACH */}
      <div className='col-12'>
        <div className='card border'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-5'>
              <KTSVG
                path='/media/icons/duotune/finance/fin001.svg'
                className='svg-icon-2 text-info me-2'
              />
              <h4 className='fw-bold m-0 text-gray-800'>ACH</h4>
            </div>
            <div className='row'>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/general/gen043.svg'
                  label='ACH/E-Check Approved'
                  value={ach.achCheckApproved}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen043.svg'
                  label='ACH Form Received & Uploaded'
                  value={ach.achFormReceivedAndUploaded}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/communication/com006.svg'
                  label='ACH/E-Check Approved By'
                  value={ach.achCheckApprovedBy}
                />
                <InfoField
                  icon='/media/icons/duotune/communication/com006.svg'
                  label='ACH Form Uploaded By'
                  value={ach.achFormUploadedBy}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/general/gen014.svg'
                  label='ACH/E-Checked Approved Date & Time'
                  value={ach.achCheckApprovedDateTime}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen014.svg'
                  label='ACH Form Received Uploaded Date'
                  value={ach.achFormReceivedUploadedDate}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Net Terms */}
      <div className='col-12'>
        <div className='card border'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-5'>
              <KTSVG
                path='/media/icons/duotune/finance/fin003.svg'
                className='svg-icon-2 text-primary me-2'
              />
              <h4 className='fw-bold m-0 text-gray-800'>Net Terms</h4>
            </div>
            <div className='row'>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/general/gen043.svg'
                  label='Net Term Received & Uploaded'
                  value={netTerms.netTermReceivedAndUploaded}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/communication/com006.svg'
                  label='Net Term Agreement Uploaded By'
                  value={netTerms.netTermAgreementUploadedBy}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/general/gen014.svg'
                  label='Net Term Uploaded Time'
                  value={netTerms.netTermUploadedTime}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FinancialPaymentSection
