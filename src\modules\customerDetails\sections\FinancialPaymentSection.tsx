import React from 'react'
import {KTSVG} from '../../../_metronic/helpers'

const InfoField = ({
  icon,
  label,
  value,
  className = '',
}: {
  icon?: string
  label: string
  value: string
  className?: string
}) => (
  <div className={`d-flex align-items-center mb-3 ${className}`}>
    {icon && (
      <div className='me-3'>
        <KTSVG path={icon} className='svg-icon-5 text-muted' />
      </div>
    )}
    <div className='flex-grow-1'>
      <span className='text-gray-700 fs-6'>{label}:</span>
      <span className='text-gray-800 fw-semibold ms-2'>{value}</span>
    </div>
  </div>
)

const FinancialPaymentSection = () => (
  <div className='row g-5 mb-5'>
    {/* Payment & Credit */}
    <div className='col-12'>
      <div className='card border-0 bg-light'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center mb-5'>
            <KTSVG
              path='/media/icons/duotune/finance/fin010.svg'
              className='svg-icon-2 text-primary me-2'
            />
            <h4 className='fw-bold m-0 text-gray-800'>Payment & Credit</h4>
          </div>
          <div className='row'>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/finance/fin006.svg'
                label='Payment Term'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/finance/fin010.svg'
                label='Payment Term Notes'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='Tax Exempt'
                value='No'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/finance/fin010.svg'
                label='Credit Limit'
                value='$50,000'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Payment Term Approved By'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/finance/fin006.svg'
                label='Currency'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/finance/fin006.svg'
                label='Unpaid Balance'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='Payment Term Approved Date & Time'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Payment Term Risk'
                value='Not Set'
              />
            </div>
          </div>
          <div className='row mt-4'>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/finance/fin006.svg'
                label='Outstanding Balance'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Payment Term Issues'
                value='Not Set'
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Cash & Carry */}
    <div className='col-12'>
      <div className='card border-0 bg-light'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center mb-5'>
            <KTSVG
              path='/media/icons/duotune/finance/fin006.svg'
              className='svg-icon-2 text-warning me-2'
            />
            <h4 className='fw-bold m-0 text-gray-800'>Cash & Carry</h4>
          </div>
          <div className='row'>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Cash & Carry Check Approved?'
                value='No'
              />
              <InfoField
                icon='/media/icons/duotune/finance/fin006.svg'
                label='Check Approved Amount Limit'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/communication/com006.svg'
                label='Check Approved By'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Noted for Check/CNC'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='Check Approved Date & Time'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Does The Customer have account w/CTOS?'
                value='Not Set'
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Credit Card */}
    <div className='col-12'>
      <div className='card border-0 bg-light'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center mb-5'>
            <KTSVG
              path='/media/icons/duotune/finance/fin002.svg'
              className='svg-icon-2 text-success me-2'
            />
            <h4 className='fw-bold m-0 text-gray-800'>Credit Card</h4>
          </div>
          <div className='row'>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='Customer Credit Card Fees Exempt'
                value='No'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/communication/com006.svg'
                label='Credit Card Fees Exempt Approved By'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='Credit Card Fees Exempt Approved Date'
                value='Not Set'
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* ACH */}
    <div className='col-12'>
      <div className='card border-0 bg-light'>
        <div className='card-body p-6'>
          <div className='d-flex align-items-center mb-5'>
            <KTSVG
              path='/media/icons/duotune/finance/fin001.svg'
              className='svg-icon-2 text-info me-2'
            />
            <h4 className='fw-bold m-0 text-gray-800'>ACH</h4>
          </div>
          <div className='row'>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='ACH/E-Check Approved'
                value='No'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen043.svg'
                label='ACH Form Received & Uploaded'
                value='No'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/communication/com006.svg'
                label='ACH/E-Check Approved By'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/communication/com006.svg'
                label='ACH Form Uploaded By'
                value='Not Set'
              />
            </div>
            <div className='col-md-4'>
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='ACH/E-Checked Approved Date & Time'
                value='Not Set'
              />
              <InfoField
                icon='/media/icons/duotune/general/gen014.svg'
                label='ACH Form Received Uploaded Date'
                value='Not Set'
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
)

export default FinancialPaymentSection
