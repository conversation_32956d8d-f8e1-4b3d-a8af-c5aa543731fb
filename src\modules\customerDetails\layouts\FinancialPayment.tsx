import React from 'react'
import FinancialPaymentSection from '../sections/FinancialPaymentSection'
import useGetCustomerFinancialPayment from '../hooks/useGetCustomerFinancialPayment'
import {FinancialPaymentContext} from '../context'

const FinancialPayment = () => {
  const {financialPayment, isLoading} = useGetCustomerFinancialPayment()
  const contextValue = {financialPayment, isLoading}
  return (
    <FinancialPaymentContext.Provider value={contextValue}>
      <FinancialPaymentSection />
    </FinancialPaymentContext.Provider>
  )
}

export default FinancialPayment
