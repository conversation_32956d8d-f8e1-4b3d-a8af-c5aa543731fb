import {toggleSortingOrder} from '../../../utils/common'
import Loading from '../../loading'
import {RenderBothArrow} from '../../../utils/renderBothArrow'
import NoDataFound from '../../../components/NoDataFound'
import {useGetRewards} from '../hooks/useGetRewards'
import CustomerRewardsTableRow from './CustomerRewardsTableRow'
import {Pagination} from '../../../utils/pagination'

const CustomerRewardsTable = ({customer_id}:any) => {
  const {rewards, onSortingChange, filters, isFetchingRewardData, pagination, onPageChange}: any =
    useGetRewards(customer_id)

  return (
    <div className=''>
      <h3 className='mb-5'>Reward History</h3>

      <div className='table-responsive position-relative'>
        <div className='table-loader-wrapper'>
          <table className='table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4 mb-15'>
            <thead className='table-row-bordered text-uppercase'>
              <tr className='fs-6 fw-semibold text-muted'>
                <th
                  onClick={() => onSortingChange('created_at', toggleSortingOrder(filters['sort_order']))}
                  className='min-w-120px'
                >
                  <div className='d-flex align-items-center'>
                    <span className='me-2'>{'Date'}</span>

                    {filters['sort_order'] === 'asc' && filters['sort_by'] === 'created_at' ? (
                      <i className='bi bi-caret-up-fill fs-8'></i>
                    ) : filters['sort_order'] === 'desc' && filters['sort_by'] === 'created_at' ? (
                      <i className='bi bi-caret-down-fill fs-8'></i>
                    ) : (
                      <RenderBothArrow />
                    )}
                  </div>
                </th>
                <th className='min-w-150px'>{'Order no.'}</th>
                <th
                  onClick={() =>
                    onSortingChange('order_status', toggleSortingOrder(filters['sort_order']))
                  }
                  className='min-w-150px'
                >
                  <div className='d-flex align-items-center'>
                    <span className='me-2'>{'Order Type'}</span>

                    {filters['sort_order'] === 'asc' && filters['sort_by'] === 'order_status' ? (
                      <i className='bi bi-caret-up-fill fs-8'></i>
                    ) : filters['sort_order'] === 'desc' &&
                      filters['sort_by'] === 'order_status' ? (
                      <i className='bi bi-caret-down-fill fs-8'></i>
                    ) : (
                      <RenderBothArrow />
                    )}
                  </div>
                </th>
                <th className='min-w-150px'>{'order value'}</th>
                <th className='min-w-150px'>{'Coupon Code'}</th>
                <th className='min-w-150px'>{'Rewards'}</th>
                <th className='min-w-150px'>{'Redemption'}</th>
                <th className='min-w-150px'>{'balance'}</th>
              </tr>
            </thead>
            <tbody>
              {rewards && rewards?.length > 0
                ? rewards?.map((row: any) => <CustomerRewardsTableRow row={row} key={row['id']} />)
                : !isFetchingRewardData && <NoDataFound colspan={8} />}
            </tbody>
          </table>
          {isFetchingRewardData && <Loading />}
        </div>
      </div>

      <Pagination pagination={pagination} onPageChange={onPageChange} />
    </div>
  )
}

export {CustomerRewardsTable}
