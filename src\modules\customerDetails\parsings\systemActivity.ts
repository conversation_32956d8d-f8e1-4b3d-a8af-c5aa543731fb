import { ISystemActivity, IActivityItem, ICustomerNote } from '../interfaces'

export const parseCustomerSystemActivity = (data: any): ISystemActivity => {
  if (!data) return {} as ISystemActivity
  
  return {
    systemInformation: {
      attempt1DateCapture: data.system_information?.attempt1_date_capture || data.systemInformation?.attempt1DateCapture || '',
      createdBy: data.system_information?.created_by || data.systemInformation?.createdBy || '',
      lastModifiedBy: data.system_information?.last_modified_by || data.systemInformation?.lastModifiedBy || '',
      userWhoLastModifiedNotes: data.system_information?.user_who_last_modified_notes || data.systemInformation?.userWhoLastModifiedNotes || '',
      notesLastModifiedDateTime: data.system_information?.notes_last_modified_date_time || data.systemInformation?.notesLastModifiedDateTime || '',
    },
    activityTimeline: parseActivityItems(data.activity_timeline || data.activityTimeline || []),
    customerNotes: parseCustomerNotes(data.customer_notes || data.customerNotes || []),
  }
}

export const parseActivityItems = (items: any[]): IActivityItem[] => {
  if (!Array.isArray(items)) return []
  
  return items.map(item => ({
    id: item.id || '',
    type: item.type || 'note',
    title: item.title || '',
    description: item.description || '',
    createdBy: item.created_by || item.createdBy || '',
    createdDate: item.created_date || item.createdDate || '',
    status: item.status || '',
  }))
}

export const parseCustomerNotes = (notes: any[]): ICustomerNote[] => {
  if (!Array.isArray(notes)) return []
  
  return notes.map(note => ({
    id: note.id || '',
    author: note.author || '',
    note: note.note || '',
    createdDate: note.created_date || note.createdDate || '',
    isPrivate: note.is_private || note.isPrivate || false,
  }))
}
