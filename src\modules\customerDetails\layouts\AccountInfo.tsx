import React from 'react'
import AccountInfoSection from '../sections/AccountInfoSection'
import useGetCustomerAccountInfo from '../hooks/useGetCustomerAccountInfo'
import {AccountInfoContext} from '../context'

const AccountInfo = () => {
  const {accountInfo, isLoading} = useGetCustomerAccountInfo()
  const contextValue = {accountInfo, isLoading}
  return (
    <AccountInfoContext.Provider value={contextValue}>
      <AccountInfoSection accountInfo={accountInfo} isLoading={isLoading} />
    </AccountInfoContext.Provider>
  )
}

export default AccountInfo
