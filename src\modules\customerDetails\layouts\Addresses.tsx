import AddressesSection from '../sections/AddressesSection'
import useGetCustomerAddresses from '../hooks/useGetCustomerAddresses'
import {AddressesContext} from '../context'

const Addresses = () => {
  const {addresses, isLoading} = useGetCustomerAddresses()
  const contextValue = {
    addresses,
    isLoading,
  }

  return (
    <AddressesContext.Provider value={contextValue}>
      <AddressesSection />
    </AddressesContext.Provider>
  )
}

export default Addresses
