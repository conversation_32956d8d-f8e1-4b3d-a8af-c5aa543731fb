import React from 'react'
import {KTSVG} from '../../../_metronic/helpers'

const OrdersSection = () => (
  <div className='card card-flush border'>
    <div className='card-header border-0 pt-6'>
      <div className='card-title'>
        <div className='d-flex align-items-center'>
          <KTSVG
            path='/media/icons/duotune/ecommerce/ecm002.svg'
            className='svg-icon-2 text-primary me-2'
          />
          <h3 className='fw-bold m-0'>Orders</h3>
        </div>
      </div>
    </div>
    <div className='card-body pt-0'>
      <div className='table-responsive'>
        <table className='table align-middle table-row-dashed fs-6 gy-5'>
          <thead>
            <tr className='text-start text-muted fw-bold fs-7 text-uppercase gs-0'>
              <th className='min-w-125px'>ORDER ID</th>
              <th className='min-w-125px'>CUSTOMER</th>
              <th className='min-w-125px'>DATE</th>
              <th className='min-w-100px'>STATUS</th>
              <th className='min-w-100px'>PAYMENT</th>
              <th className='min-w-100px'>METHOD</th>
              <th className='min-w-100px'>TOTAL</th>
              <th className='min-w-100px'>ITEMS</th>
              <th className='min-w-125px'>SHIP TO</th>
              <th className='min-w-100px'>CHANNEL</th>
              <th className='min-w-100px'>ACTIONS</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td colSpan={11} className='text-center py-20'>
                <div className='d-flex flex-column align-items-center'>
                  <div className='symbol symbol-100px bg-light-primary mb-5'>
                    <div className='symbol-label'>
                      <KTSVG
                        path='/media/icons/duotune/ecommerce/ecm002.svg'
                        className='svg-icon-3x text-primary'
                      />
                    </div>
                  </div>
                  <div className='fw-bold fs-3 text-gray-800 mb-2'>No orders found</div>
                  <div className='text-muted fs-6'>
                    This customer has not placed any orders yet.
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
)

export default OrdersSection
