import {useState, useContext} from 'react'
import SoldProductTableRow from './SoldProductTableRow'
import {SoldProductListingContext} from '../../context'
import SoldProductsListingHeader from '../SoldProductsListingHeader'
import {toggleSortingOrder} from '../../../../utils/common'
import Loading from '../../../loading'
import {RenderBothArrow} from '../../../../utils/renderBothArrow'
import NoDataFound from '../../../../components/NoDataFound'
import usePermission from '../../../../hook/usePermission'

function SoldProductsTable() {
  const {soldProducts, onSortingChange, filters, isLoading}: any =
  useContext(SoldProductListingContext)
  const [checkedIds, setCheckedIds] = useState([])
  const [checkedSkuIds, setCheckedSkuIds] = useState([])
  const {hasPermission} = usePermission()


  const handleCheckboxChange = (id: any) => {
    setCheckedIds((prevIds: any) =>
      prevIds.includes(id) ? prevIds.filter((item: any) => item !== id) : [...prevIds, id]
    )
  }

  const handleSkuCheckboxChange = (sku: any) => {
    setCheckedSkuIds((skus: any) =>
      skus.includes(sku) ? skus.filter((item: any) => item !== sku) : [...skus, sku]
    )
  }

  
  const selectAllIds = (row: any) => {
    const allIds: any = []

    row.forEach((element: any) => {
      allIds.push(element['id'])
    })
    
    setCheckedIds(allIds)
  }

  const selectAllSkus = (row: any) => {
    const allSkus: any = []
    row.forEach((element: any) => {
      allSkus.push(element['sku'])
    })
    setCheckedSkuIds(allSkus)
  }

  const deSelectAllIds = () => {
    setCheckedIds([])
  }

  const deSelectAllSkus = () => {
    setCheckedSkuIds([])
  }

  const handleAllCheckboxChange = (e: any) => { 
    e.target.checked ? selectAllIds(soldProducts) : deSelectAllIds()
    e.target.checked ? selectAllSkus(soldProducts) : deSelectAllSkus()
  }

  const handleClear = () => {
    setCheckedIds([])
    setCheckedSkuIds([])
  }

  return (
    <>
      <SoldProductsListingHeader checkedIds={checkedIds} checkedSkuIds={checkedSkuIds} handleClear={handleClear} />

      <div className='card-body p-0'>
        <div className='table-responsive position-relative'>
          <div className='table-loader-wrapper'>
            <table className='table table-row-dashed table-row-gray-300 align-middle gs-0 gy-3 mb-20'>
              <thead className='table-row-bordered'>
                <tr className='fs-6 fw-semibold text-muted text-uppercase'>
                  <th className='w-35px'>
                    <div className='form-check form-check-sm form-check-custom form-check-solid'>
                    {hasPermission('analytics_sold products', 'write') ? (

                      <input
                        className='form-check-input'
                        type='checkbox'
                        onChange={(e) => handleAllCheckboxChange(e)
                        }
                      /> )
                      :
                      (
                        <input
                        className='form-check-input'
                        type='checkbox'
                        disabled={true}
                      /> 
                      )}
                    </div>
                  </th>
                  <th className='min-w-450px'>{'PRODUCT NAME'}</th>
                  {/* <th className='min-w-175px w-150px'>{'SKU'}</th> */}
                  <th
                    className={`min-w-175px`}
                    onClick={() =>
                      onSortingChange('sku', toggleSortingOrder(filters['sort_order']))
                    }
                  >
                    <div className='d-flex align-items-center'>
                      <span className='me-2'>{'SKU'}</span>

                      {filters['sort_order'] === 'asc' && filters['sort_by'] === 'sku' ? (
                        <i className='bi bi-caret-up-fill fs-8'></i>
                      ) : filters['sort_order'] === 'desc' && filters['sort_by'] === 'sku' ? (
                        <i className='bi bi-caret-down-fill fs-8'></i>
                      ) : (
                        <RenderBothArrow />
                      )}
                    </div>
                  </th>
                  <th
                    className={`min-w-175px`}
                    onClick={() =>
                      onSortingChange('total_sold', toggleSortingOrder(filters['sort_order']))
                    }
                  >
                    <div className='d-flex align-items-center'>
                      <span className='me-2'>{'QUANTITY SOLD'}</span>

                      {filters['sort_order'] === 'asc' && filters['sort_by'] === 'total_sold' ? (
                        <i className='bi bi-caret-up-fill fs-8'></i>
                      ) : filters['sort_order'] === 'desc' &&
                        filters['sort_by'] === 'total_sold' ? (
                        <i className='bi bi-caret-down-fill fs-8'></i>
                      ) : (
                        <RenderBothArrow />
                      )}
                    </div>
                  </th>
                  <th
                    className={`min-w-150px`}
                    onClick={() =>
                      onSortingChange('inventory_level', toggleSortingOrder(filters['sort_order']))
                    }
                  >
                    <div className='d-flex align-items-center'>
                      <span className='me-2'>{'AVAILABLE QTY'}</span>

                      {filters['sort_order'] === 'asc' &&
                      filters['sort_by'] === 'inventory_level' ? (
                        <i className='bi bi-caret-up-fill fs-8'></i>
                      ) : filters['sort_order'] === 'desc' &&
                        filters['sort_by'] === 'inventory_level' ? (
                        <i className='bi bi-caret-down-fill fs-8'></i>
                      ) : (
                        <RenderBothArrow />
                      )}
                    </div>
                  </th>
                  <th
                    className={`w-100px text-center`}
                    onClick={() =>
                      onSortingChange('is_visible', toggleSortingOrder(filters['sort_order']))
                    }
                  >
                    <div className='d-flex justify-content-center align-items-center'>
                      <span className='me-2'>{'VISIBILITY'}</span>
                      {filters['sort_order'] === 'asc' && filters['sort_by'] === 'is_visible' ? (
                        <i className='bi bi-caret-up-fill fs-8'></i>
                      ) : filters['sort_order'] === 'desc' &&
                        filters['sort_by'] === 'is_visible' ? (
                        <i className='bi bi-caret-down-fill fs-8'></i>
                      ) : (
                        <RenderBothArrow />
                      )}
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                {soldProducts && soldProducts.length > 0
                  ? soldProducts?.map((row: any) => (
                      <SoldProductTableRow
                        TableRow
                        row={row}
                        key={row['id']}
                        checkedIds={checkedIds}
                        handleCheckboxChange={handleCheckboxChange}
                        handleSkuCheckboxChange={handleSkuCheckboxChange}
                      />
                    ))
                  : !isLoading && <NoDataFound colspan={6} />}
              </tbody>
            </table>
            {isLoading && <Loading />}
          </div>
        </div>
      </div>
    </>
  )
}

export default SoldProductsTable
