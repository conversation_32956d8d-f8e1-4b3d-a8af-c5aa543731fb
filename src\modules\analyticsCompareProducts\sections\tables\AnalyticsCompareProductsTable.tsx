import React, {useContext} from 'react'
import {AnalyticsCompareProductsContext} from '../../context'
import {formatPrice, formatToIndianNumber} from '../../../../utils/common'
import {ComparisonRow, buildComparisonRows, getValue} from '../../utils'
import Loading from '../../../loading'
import defaultImage from '../../../../images/default-bc-product.png'

const AnalyticsCompareProductsTable = () => {
  const {products, meta, isLoading} = useContext(AnalyticsCompareProductsContext)
  const customerTypes = meta?.customer_types || {}
  const months = meta?.month_rows || {}
  const states = meta?.states || {}
  const allRows = buildComparisonRows(customerTypes, months, states)

  // Group rows by section for proper rendering
  const groupedRows: {[key: string]: ComparisonRow[]} = {}
  allRows.forEach((row) => {
    if (!groupedRows[row.section]) {
      groupedRows[row.section] = []
    }
    groupedRows[row.section].push(row)
  })

  // Render cell content based on data type
  const renderCellContent = (product: any, row: ComparisonRow) => {
    let value = getValue(product, row.key)

    // Handle special cases
    if (row.key === 'product_image') {
      return (
        <div className='text-center'>
          {value ? (
            <img
              src={value}
              alt={product.product_name}
              className='rounded w-100px h-100px object-fit-contain'
            />
          ) : (
            <img
              src={defaultImage}
              alt={product.product_name}
              title={product.product_name}
              className='rounded w-100px h-100px object-fit-contain'
            />
          )}
        </div>
      )
    }

    // Format earnings with $ symbol
    if (row.key === 'earnings.total' || row.key.startsWith('earnings.')) {
      value = value !== '-' ? formatPrice(value, false) : value
    }

    // Handle object values
    if (typeof value === 'object' && value !== null) {
      return '-'
    }

    // Format numbers using Indian number format
    if (typeof value === 'number' && !isNaN(value)) {
      return formatToIndianNumber(value)
    }

    return value
  }

  return (
    <div className='card'>
      {isLoading && <Loading />}
      <div className='card-body p-0'>
        <div className='table-responsive'>
          <table
            className='table table-bordered align-middle fs-6 mb-0'
            style={{borderCollapse: 'separate', borderSpacing: 0}}
          >
            {/* Header with product information */}
            <thead className='bg-light'>
              <tr>
                <th className='border-end fw-bold text-dark min-w-200px head-col-sticky'></th>
                <th className='border-end fw-bold text-dark min-w-200px head-col-sticky'></th>
                {products?.map((product: any, index: number) => (
                  <th key={product.product_id} className='text-center fw-bold text-dark border-end'>
                    Product {index + 1}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {Object.entries(groupedRows)?.map(([sectionName, sectionRows]) => {
                let isFirstRowInSection = true
                // Add dividers before these sections
                const sectionsNeedingDivider = ['Sales by State', 'Inventory Info']
                const needsDivider = sectionsNeedingDivider.includes(sectionName)

                return (
                  <React.Fragment key={sectionName}>
                    {/* Add divider row before sections that need it */}
                    {needsDivider && (
                      <tr>
                        <td colSpan={2 + products?.length} className='p-0'>
                          <div className='border-top border-3 border-secondary my-2'></div>
                        </td>
                      </tr>
                    )}

                    {sectionRows?.map((row, rowIndex) => {
                      const isFirstRow = isFirstRowInSection
                      isFirstRowInSection = false

                      return (
                        <tr key={`${sectionName}-${rowIndex}`} className='border-bottom'>
                          {/* Section Title Column only show for first row of each section */}
                          {isFirstRow ? (
                            <td
                              className='fw-bold text-dark bg-light border-end align-top min-w-200px p-0 body-col-sticky pt-2 ps-2'
                              rowSpan={sectionRows?.length}
                            >
                              {sectionName}
                            </td>
                          ) : null}

                          {/* Attribute Label Column */}
                          <td className='fw-semibold text-dark bg-light border-end  min-w-200px p-0 body-col-sticky ps-2'>
                            {row.label}
                          </td>

                          {/* Product Data Columns */}
                          {products?.map((product: any) => (
                            <td key={product.product_id} className='text-center border-end'>
                              {renderCellContent(product, row)}
                            </td>
                          ))}
                        </tr>
                      )
                    })}
                  </React.Fragment>
                )
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default AnalyticsCompareProductsTable
