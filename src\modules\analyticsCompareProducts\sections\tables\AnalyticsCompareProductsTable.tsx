import {useContext} from 'react'
import {AnalyticsCompareProductsContext} from '../../context'

interface DynamicRow {
  section: string
  label: string
  key: string
}

const METRIC_ROWS = [
  {section: 'Product Info', label: 'Product Image', key: 'product_image'},
  {section: 'Product Info', label: 'Product Name', key: 'product_name'},
  {section: 'Product Info', label: 'Product SKU', key: 'product_sku'},
  {section: 'Orders', label: 'Total Orders Placed', key: 'orders.total_orders'},
  // Orders by customer type will be rendered dynamically
  {
    section: 'Quantity Sold',
    label: 'Total Quantity Sold',
    key: 'quantity_sold.total_quantity_sold',
  },
  // Quantity sold by customer type will be rendered dynamically
  {section: 'Earnings', label: 'Total Earnings', key: 'earnings.total_earnings'},
  // Earnings by customer type will be rendered dynamically
  {
    section: 'Total Sold Quantity',
    label: 'Total Quantity (All Variants)',
    key: 'total_quantity_sold',
  },
  {section: 'Total Sold Quantity', label: 'Current Month Sales', key: 'monthly_sales.month_1'},
  // { section: 'Total Sold Quantity', label: 'Monthly Sales (Last 6 Months)', key: 'monthly_sales' }, // REMOVE
  // { section: 'Sales by State', label: 'Quantity Sold per State', key: 'sales_by_state' }, // REMOVE
  {
    section: 'Inventory Info',
    label: 'Available Quantity',
    key: 'inventory_info.available_quantity',
  },
  {section: 'Inventory Info', label: 'Blocked Quantity', key: 'inventory_info.blocked_quantity'},
  {section: 'Inventory Info', label: 'Incoming Quantity', key: 'inventory_info.incoming_quantity'},
]

const AnalyticsCompareProductsTable = () => {
  const {products, meta, isLoading} = useContext(AnalyticsCompareProductsContext)
  const customerTypes = meta?.customer_types || {}
  const months = meta?.month_rows || {}
  const states = meta?.states || {}

  if (isLoading) {
    return (
      <div className='d-flex justify-content-center py-10'>
        <div className='spinner-border text-primary'></div>
      </div>
    )
  }

  // Build dynamic metric rows for customer types, months, and states
  const dynamicRows: DynamicRow[] = []

  // Orders by customer type
  Object.entries(customerTypes).forEach(([key, label]) => {
    dynamicRows.push({
      section: 'Orders',
      label: `- ${String(label)[0].toUpperCase() + String(label).slice(1).replace(/_/g, ' ')}`,
      key: `orders.${key}`,
    })
  })
  // Quantity sold by customer type
  Object.entries(customerTypes).forEach(([key, label]) => {
    dynamicRows.push({
      section: 'Quantity Sold',
      label: `- ${String(label)[0].toUpperCase() + String(label).slice(1).replace(/_/g, ' ')}`,
      key: `quantity_sold.${key}`,
    })
  })
  // Earnings by customer type
  Object.entries(customerTypes).forEach(([key, label]) => {
    dynamicRows.push({
      section: 'Earnings',
      label: `- ${String(label)[0].toUpperCase() + String(label).slice(1).replace(/_/g, ' ')}`,
      key: `earnings.${key}`,
    })
  })

  // Compose all rows, SKIP monthly_sales and sales_by_state as generic rows
  const allRows = [
    ...METRIC_ROWS.slice(0, 4),
    ...dynamicRows.filter((r) => r.section === 'Orders'),
    ...METRIC_ROWS.slice(4, 5),
    ...dynamicRows.filter((r) => r.section === 'Quantity Sold'),
    ...METRIC_ROWS.slice(5, 6),
    ...dynamicRows.filter((r) => r.section === 'Earnings'),
    ...METRIC_ROWS.slice(6, 8), // Only up to 'Current Month Sales'
    ...METRIC_ROWS.slice(8), // Inventory Info
  ]

  // Helper to get nested value
  const getValue = (obj: any, path: string) => {
    return path
      .split('.')
      .reduce((acc, part) => (acc && acc[part] !== undefined ? acc[part] : '-'), obj)
  }

  return (
    <div className='table-responsive'>
      <table className='table align-middle table-row-dashed fs-6 gy-5'>
        <thead>
          <tr>
            <th className='min-w-200px'>Metric</th>
            {products.map((p: any) => (
              <th key={p.product_id} className='min-w-200px text-center'>
                <div className='fw-bold'>{p.product_name}</div>
                <div className='text-muted fs-7'>{p.product_sku}</div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {allRows.map((row, idx) => (
            <tr key={row.key + idx}>
              <td className='fw-semibold'>{row.label}</td>
              {products.map((p: any) => {
                let value = getValue(p, row.key)
                if (row.key === 'product_image') {
                  return (
                    <td key={p.product_id} className='text-center'>
                      <img
                        src={value}
                        alt={p.product_name}
                        style={{width: 48, height: 48, objectFit: 'contain'}}
                      />
                    </td>
                  )
                }
                if (row.key === 'earnings.total_earnings' || row.key.startsWith('earnings.')) {
                  value = value !== '-' ? `$${value}` : value
                }
                // Only render primitive values
                if (typeof value === 'object' && value !== null) {
                  return (
                    <td key={p.product_id} className='text-center'>
                      -
                    </td>
                  )
                }
                return (
                  <td key={p.product_id} className='text-center'>
                    {value}
                  </td>
                )
              })}
            </tr>
          ))}
          {/* Monthly sales breakdown */}
          <tr>
            <td className='fw-semibold'>Monthly Sales (Last 6 Months)</td>
            {products.map((p: any) => (
              <td key={p.product_id} className='text-center'>
                <div className='d-flex flex-column gap-1'>
                  {Object.entries(months).map(([monthKey, monthLabel]:any) => (
                    <div key={monthKey} className='d-flex justify-content-between'>
                      <span className='text-muted fs-7'>{monthLabel}:</span>
                      <span className='fw-bold ms-2'>{p.monthly_sales[monthKey]}</span>
                    </div>
                  ))}
                </div>
              </td>
            ))}
          </tr>
          {/* Sales by state breakdown */}
          <tr>
            <td className='fw-semibold'>Quantity Sold per State</td>
            {products.map((p: any) => (
              <td key={p.product_id} className='text-center'>
                <div className='d-flex flex-column gap-1'>
                  {Object.entries(states).map(([stateKey, stateLabel]: any) => (
                    <div key={stateKey} className='d-flex justify-content-between'>
                      <span className='text-muted fs-7'>{stateLabel}:</span>
                      <span className='fw-bold ms-2'>{p.sales_by_state[stateKey]}</span>
                    </div>
                  ))}
                </div>
              </td>
            ))}
          </tr>
        </tbody>
      </table>
    </div>
  )
}

export default AnalyticsCompareProductsTable
