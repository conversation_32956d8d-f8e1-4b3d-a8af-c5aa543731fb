import React, {useContext} from 'react'
import {AnalyticsCompareProductsContext} from '../../context'
import {formatPrice} from '../../../../utils/common'

interface ComparisonRow {
  section: string
  label: string
  key: string
  isSection?: boolean
}

// Define the comparison structure following Flipkart-style layout
const COMPARISON_STRUCTURE: ComparisonRow[] = [
  // Product Info Section
  {section: 'Product Info', label: 'Product Image', key: 'product_image'},
  {section: 'Product Info', label: 'Product Name', key: 'product_name'},
  {section: 'Product Info', label: 'Product SKU', key: 'product_sku'},

  // Orders Section
  {section: 'Orders', label: 'Total Orders Placed', key: 'orders.total'},

  // Quantity Sold Section  
  {
    section: 'Quantity Sold',
    label: 'Total Quantity Sold',
    key: 'quantity_sold.total',
  },

  // Earnings Section
  {section: 'Earnings', label: 'Total Earnings', key: 'earnings.total'},

  // Total Sold Quantity Section
  {
    section: 'Total Sold Quantity',
    label: 'Total Quantity (All Variants)',
    key: 'total_sold_quantity',
  },
  {section: 'Total Sold Quantity', label: 'Monthly Sales (Last 6 Months)', key: 'monthly_sales'},

  // Sales by State Section
  {section: 'Sales by State', label: 'Quantity Sold per State', key: 'sales_by_state'},

  // Inventory Info Section
  {
    section: 'Inventory Info',
    label: 'Available Quantity',
    key: 'inventory_info.available_quantity',
  },
  {section: 'Inventory Info', label: 'Blocked Quantity', key: 'inventory_info.blocked_quantity'},
  {section: 'Inventory Info', label: 'Incoming Quantity', key: 'inventory_info.incoming_quantity'},
]

const AnalyticsCompareProductsTable = () => {
  const {products, meta, isLoading} = useContext(AnalyticsCompareProductsContext)
  const customerTypes = meta?.customer_types || {}
  const months = meta?.month_rows || {}
  const states = meta?.states || {}

  if (isLoading) {
    return (
      <div className='d-flex justify-content-center py-10'>
        <div className='spinner-border text-primary'></div>
      </div>
    )
  }

  // Helper to get nested value
  const getValue = (obj: any, path: string) => {
    return path
      .split('.')
      .reduce((acc, part) => (acc && acc[part] !== undefined ? acc[part] : '-'), obj)
  }

  // Build all comparison rows with dynamic customer type, month, and state rows
  const buildComparisonRows = () => {
    const rows: ComparisonRow[] = []

    // Add basic structure rows and inject dynamic rows where needed
    COMPARISON_STRUCTURE.forEach((row) => {
      // Skip the generic monthly_sales and sales_by_state rows
      if (row.key === 'monthly_sales' || row.key === 'sales_by_state') {
        return
      }

      rows.push(row)

      // Add customer type sub-rows after main category rows
      if (row.key === 'orders.total') {
        Object.entries(customerTypes).forEach(([key, label]) => {
          rows.push({
            section: 'Orders',
            label: `${String(label)[0].toUpperCase() + String(label).slice(1).replace(/_/g, ' ')}`,
            key: `orders.${key}`,
          })
        })
      } else if (row.key === 'quantity_sold.total') {
        Object.entries(customerTypes).forEach(([key, label]) => {
          rows.push({
            section: 'Quantity Sold',
            label: `${String(label)[0].toUpperCase() + String(label).slice(1).replace(/_/g, ' ')}`,
            key: `quantity_sold.${key}`,
          })
        })
      } else if (row.key === 'earnings.total') {
        Object.entries(customerTypes).forEach(([key, label]) => {
          rows.push({
            section: 'Earnings',
            label: `${String(label)[0].toUpperCase() + String(label).slice(1).replace(/_/g, ' ')}`,
            key: `earnings.${key}`,
          })
        })
      } else if (row.key === 'total_sold_quantity') {
        // Add individual month rows after Total Quantity (All Variants)
        Object.entries(months).forEach(([monthKey, monthLabel]) => {
          rows.push({
            section: 'Total Sold Quantity',
            label: String(monthLabel),
            key: `monthly_sales.${monthKey}`,
          })
        })
      }
    })

    // Add Sales by State section with individual state rows
    Object.entries(states).forEach(([stateKey, stateLabel]) => {
      rows.push({
        section: 'Sales by State',
        label: String(stateLabel),
        key: `sales_by_state.${stateKey}`,
      })
    })

    return rows
  }

  const allRows = buildComparisonRows()

  // Group rows by section for proper rendering
  const groupedRows: {[key: string]: ComparisonRow[]} = {}
  allRows.forEach((row) => {
    if (!groupedRows[row.section]) {
      groupedRows[row.section] = []
    }
    groupedRows[row.section].push(row)
  })

  // Render cell content based on data type
  const renderCellContent = (product: any, row: ComparisonRow) => {
    let value = getValue(product, row.key)

    // Handle special cases
    if (row.key === 'product_image') {
      return (
        <div className='text-center'>
          <img
            src={value}
            alt={product.product_name}
            className='rounded w-100px h-100px object-fit-contain'
          />
        </div>
      )
    }

    // Format earnings with $ symbol
    if (row.key === 'earnings.total' || row.key.startsWith('earnings.')) {
      value = value !== '-' ? formatPrice(value, false) : value
    }

    // Handle object values
    if (typeof value === 'object' && value !== null) {
      return '-'
    }

    return value
  }

  return (
    <div className='card'>
      <div className='card-body p-0'>
        <div className='table-responsive'>
          <table
            className='table table-bordered align-middle fs-6 mb-0'
            style={{borderCollapse: 'separate', borderSpacing: 0}}
          >
            {/* Header with product information */}
            <thead className='bg-light'>
              <tr>
                <th className='border-end fw-bold text-dark min-w-200px head-col-sticky'></th>
                <th className='border-end fw-bold text-dark min-w-200px head-col-sticky'></th>
                {products?.map((product: any, index: number) => (
                  <th key={product.product_id} className='text-center fw-bold text-dark border-end'>
                    Product {index + 1}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {Object.entries(groupedRows)?.map(([sectionName, sectionRows]) => {
                let isFirstRowInSection = true
                // Add dividers before these sections
                const sectionsNeedingDivider = ['Sales by State', 'Inventory Info']
                const needsDivider = sectionsNeedingDivider.includes(sectionName)

                return (
                  <React.Fragment key={sectionName}>
                    {/* Add divider row before sections that need it */}
                    {needsDivider && (
                      <tr>
                        <td colSpan={2 + products?.length} className='p-0'>
                          <div className='border-top border-3 border-secondary my-2'></div>
                        </td>
                      </tr>
                    )}

                    {sectionRows?.map((row, rowIndex) => {
                      const isFirstRow = isFirstRowInSection
                      isFirstRowInSection = false

                      return (
                        <tr key={`${sectionName}-${rowIndex}`} className='border-bottom'>
                          {/* Section Title Column only show for first row of each section */}
                          {isFirstRow ? (
                            <td
                              className='fw-bold text-dark bg-light border-end align-top min-w-200px p-0 body-col-sticky'
                              rowSpan={sectionRows?.length}
                            >
                              {sectionName}
                            </td>
                          ) : null}

                          {/* Attribute Label Column */}
                          <td className='fw-semibold text-dark bg-light border-end align-top min-w-200px p-0 body-col-sticky'>
                            {row.label}
                          </td>

                          {/* Product Data Columns */}
                          {products?.map((product: any) => (
                            <td key={product.product_id} className='text-center border-end'>
                              {renderCellContent(product, row)}
                            </td>
                          ))}
                        </tr>
                      )
                    })}
                  </React.Fragment>
                )
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default AnalyticsCompareProductsTable
