import React, {useContext} from 'react'
import {AnalyticsCompareProductsContext} from '../../context'
import {formatPrice, formatToIndianNumber} from '../../../../utils/common'
import {ComparisonRow, buildComparisonRows, getValue} from '../../utils'
import Loading from '../../../loading'
import defaultImage from '../../../../images/default-bc-product.png'

const AnalyticsCompareProductsTable = () => {
  const {products, meta, isLoading} = useContext(AnalyticsCompareProductsContext)
  console.log('07-10 products: ', products)
  const customerTypes = meta?.customer_types || {}
  const months = meta?.month_rows || {}
  const states = meta?.states || {}
  const allRows = buildComparisonRows(customerTypes, months, states)

  // Group rows by section for proper rendering
  const groupedRows: {[key: string]: ComparisonRow[]} = {}
  allRows.forEach((row) => {
    if (!groupedRows[row.section]) {
      groupedRows[row.section] = []
    }
    groupedRows[row.section].push(row)
  })

  // Render cell content based on data type
  const renderCellContent = (product: any, row: ComparisonRow) => {
    let value = getValue(product, row.key)

    // Handle special cases
    if (row.key === 'product_image') {
      return (
        <div className='text-center'>
          {value ? (
            <img
              src={value}
              alt={product.product_name}
              className='rounded w-100px h-100px object-fit-contain'
            />
          ) : (
            <img
              src={defaultImage}
              alt={product.product_name}
              title={product.product_name}
              className='rounded w-100px h-100px object-fit-contain'
            />
          )}
        </div>
      )
    }

    // Format earnings with $ symbol
    if (row.key === 'earnings.total' || row.key.startsWith('earnings.')) {
      value = value !== '-' ? formatPrice(value, false) : value
    }

    // Handle object values
    if (typeof value === 'object' && value !== null) {
      return '-'
    }

    // Format numbers using Indian number format
    if (typeof value === 'number' && !isNaN(value)) {
      return formatToIndianNumber(value)
    }

    return value
  }
  if (isLoading && !products)
    return (
      <div className='position-relative'>
        <Loading />
      </div>
    )
  return (
    <div className='card'>
      <div className='card-body p-0 compare-table-loader-wrapper position-relative'>
        {isLoading && <Loading />}

        <div className='table-responsive sold-product-compare-table-wrapper'>
          <table className='table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4 mb-15 sold-product-compare-table'>
            {/* Header with product information */}
            <thead className=''>
              <tr>
                <th className='min-w-200px w-200px mw-200px'></th>
                <th className='min-w-200px'></th>
                {products?.map((product: any, index: number) => (
                  <th
                    key={product.product_id}
                    className='text-center fw-bold text-dark min-w-250px'
                  >
                    {/* Product {index + 1} */}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {Object.entries(groupedRows)?.map(([sectionName, sectionRows]) => {
                let isFirstRowInSection = true
                // Add dividers before these sections
                const sectionsNeedingDivider = [
                  'Sales by State',
                  'Inventory Info',
                  'Orders',
                  'Quantity Sold',
                  'Earnings',
                  'Total Sold Quantity',
                ]
                const needsDivider = sectionsNeedingDivider.includes(sectionName)

                return (
                  <React.Fragment key={sectionName}>
                    {/* Add divider row before sections that need it */}
                    {needsDivider && (
                      <tr>
                        <td colSpan={2 + products?.length} className='p-0 bg-white p-2'>
                          <div className=''></div>
                        </td>
                      </tr>
                    )}

                    {sectionRows?.map((row, rowIndex) => {
                      const isFirstRow = isFirstRowInSection
                      isFirstRowInSection = false

                      const isTotalRow =
                        row.label.includes('Total Earnings') ||
                        row.label.includes('Total Quantity') ||
                        row.label.includes('Total Orders Placed') ||
                        row.label.includes('Total Quantity Sold')

                      return (
                        <tr
                          key={`${sectionName}-${rowIndex}`}
                          className={`${isTotalRow ? 'bg-light-primary fw-bolder' : ''} ${
                            sectionName === 'Product Info'
                              ? 'product-info-sticky-row position-relative'
                              : ''
                          } table-row-${rowIndex + 1}`}
                        >
                          {/* Section Title Column only show for first row of each section */}
                          {isFirstRow ? (
                            <td
                              className='fw-bold text-dark align-top bg-warning bg-opacity-10 ps-4 compare-table-heading'
                              rowSpan={sectionRows?.length}
                            >
                              {sectionName}
                            </td>
                          ) : null}

                          {/* Attribute Label Column */}
                          <td className='text-dark ps-4 compare-sticky-col-l2'>{row.label}</td>

                          {/* Product Data Columns */}
                          {products?.map((product: any) => {
                            // Check if this is a "total" row that needs fw-semibold styling

                            return (
                              <td key={product.product_id} className='text-center'>
                                {renderCellContent(product, row)}
                              </td>
                            )
                          })}
                        </tr>
                      )
                    })}
                  </React.Fragment>
                )
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default AnalyticsCompareProductsTable
