import useFilters from '../../../hook/useFilters'
import useApi from '../../../services/useApi'
import moment from 'moment'

export default function useAnalyticsCompareProducts() {
  const url = new URL(window.location.href)
  const params = new URLSearchParams(url.search)
  const sku = params.get('sku')
  const Api = useApi()

  const initialFilters = {
    product_skus: sku,
    start_date: moment().startOf('month').format('YYYY-MM-DD'),
    end_date: moment().format('YYYY-MM-DD'),
  }

  const {filters, onSearch, onSortingChange, onDateChange} = useFilters(initialFilters)

  const {data: response, isFetching} = Api.useGetQuery(
    `/analytics/sold-products/comparison`,
    {
      queryId: `analytics-compare-products-${sku}`,
      filters,
    },
    {enabled: !!sku}
  )

  return {
    products: response?.data,
    meta: response?.meta,
    isLoading: isFetching,
    filters,
    onSearch,
    onSortingChange,
    onDateChange,
  }
}
