import {useState} from 'react'
import {dummyCompareProductsData} from '../utils'

export default function useAnalyticsCompareProducts() {
  // For now, use dummy data
  const [filters, setFilters] = useState({search: ''})
  const [isLoading, setIsLoading] = useState(false)
  const [pagination] = useState({page: 1, limit: 10, total: 1})

  // Dummy handlers
  const onSearch = (search: string) => setFilters((f) => ({...f, search}))
  const onSortingChange = () => {}
  const onPageChange = () => {}

  return {
    products: dummyCompareProductsData.data,
    isLoading,
    filters,
    onSearch,
    onSortingChange,
    onPageChange,
    pagination,
    meta: dummyCompareProductsData.meta,
  }
}
