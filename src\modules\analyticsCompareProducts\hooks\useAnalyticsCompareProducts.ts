import useFilters from '../../../hook/useFilters'
import useApi from '../../../services/useApi'
import {parseCompareProductData} from '../parsings'
import moment from 'moment'

export default function useAnalyticsCompareProducts() {
  const url = new URL(window.location.href)
  const params = new URLSearchParams(url.search)
  const sku = params.get('sku')
  const Api = useApi()
  
  const initialFilters = {
    product_skus: sku,
    start_date: moment().startOf('month').format('YYYY-MM-DD'),
    end_date: moment().format('YYYY-MM-DD'),
  }

  const {filters, onSearch, onSortingChange,onDateChange} = useFilters(initialFilters)

  const {data: response, isFetching} = Api.useGetQuery(
    `/analytics/sold-products/comparison`,
    {
      queryId: `analytics-compare-products-${sku}`,
      filters,
    },
    {enabled: !!sku}
  )
  console.log('07-10 response: ', response);

  return {
    products: parseCompareProductData(response?.data),
    meta: response?.meta,
    isLoading: isFetching,
    filters,
    onSearch,
    onSortingChange,
    onDateChange,
  }
}
