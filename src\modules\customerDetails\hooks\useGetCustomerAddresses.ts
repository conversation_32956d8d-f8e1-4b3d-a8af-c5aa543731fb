import useFilters from '../../../hook/useFilters'
import useApi from '../../../services/useApi'
import { parseCustomerAddresses } from '../parsings/addresses'
import { IAddress } from '../interfaces'

// Dummy data for development
const dummyAddressesData = [
  {
    id: '1',
    type: 'Billing',
    address_line_1: '123 Main Street',
    address_line_2: 'Suite 100',
    city: 'New York',
    state: 'NY',
    zip_code: '10001',
    country: 'USA',
    is_default: true,
    is_active: true,
    created_date: '2023-06-15',
    last_modified_date: '2024-01-20',
  },
  {
    id: '2',
    type: 'Shipping',
    address_line_1: '456 Oak Avenue',
    address_line_2: '',
    city: 'Los Angeles',
    state: 'CA',
    zip_code: '90210',
    country: 'USA',
    is_default: false,
    is_active: true,
    created_date: '2023-08-10',
    last_modified_date: '2023-12-05',
  },
]

export default function useGetCustomerAddresses(customerId?: string) {
  const initialFilters = {
    page: 1,
    limit: 10,
  }
  const Api = useApi()
  const {filters, onSearch, onSortingChange, onPageChange} = useFilters(initialFilters)

  // For development, return dummy data instead of API call
  const useDummyData = true // Set to false when API is ready

  const {data: response, isFetching} = Api.useGetQuery(
    customerId && !useDummyData ? `/customers/${customerId}/addresses` : '',
    {
      queryId: customerId ? `customer-addresses-${customerId}` : undefined,
      filters,
      isErrorPageRedirection: false,
    },
    {
      enabled: !!customerId && !useDummyData,
    }
  )

  // Use dummy data or parsed API response
  const rawData = useDummyData ? dummyAddressesData : response?.data
  const parsedAddresses: IAddress[] = parseCustomerAddresses(rawData)

  return {
    addresses: parsedAddresses,
    isLoading: useDummyData ? false : isFetching,
    filters,
    onSearch,
    onSortingChange,
    onPageChange,
  }
}
