import useFilters from '../../../hook/useFilters'
import useApi from '../../../services/useApi'
import { parseCustomerSystemActivity } from '../parsings/systemActivity'
import { ISystemActivity } from '../interfaces'

// Dummy data for development
const dummySystemActivityData = {
  system_information: {
    attempt1_date_capture: 'Not Set',
    created_by: 'Not Set',
    last_modified_by: 'Not Set',
    user_who_last_modified_notes: 'Not Set',
    notes_last_modified_date_time: 'Not Set',
  },
  activity_timeline: [
    {
      id: '1',
      type: 'order',
      title: 'Order #ORD-003 placed for $2,850',
      description: 'by System',
      created_by: 'System',
      created_date: '10/01/2024 at 16:00:00',
      status: 'completed',
    },
    {
      id: '2',
      type: 'call',
      title: 'Phone call regarding upcoming order',
      description: 'by <PERSON>',
      created_by: '<PERSON>',
      created_date: '08/01/2024 at 19:45:00',
      status: 'completed',
    },
  ],
  customer_notes: [
    {
      id: '1',
      author: '<PERSON>',
      note: 'Customer interested in bulk pricing for Q2 orders',
      created_date: '05/01/2024, 14:30:00',
      is_private: false,
    },
  ],
}

export default function useGetCustomerSystemActivity(customerId?: string) {
  const initialFilters = {
    page: 1,
    limit: 10,
  }
  const Api = useApi()
  const {filters, onSearch, onSortingChange, onPageChange} = useFilters(initialFilters)

  // For development, return dummy data instead of API call
  const useDummyData = true // Set to false when API is ready

  const {data: response, isFetching} = Api.useGetQuery(
    customerId && !useDummyData ? `/customers/${customerId}/system-activity` : '',
    {
      queryId: customerId ? `customer-system-activity-${customerId}` : undefined,
      filters,
      isErrorPageRedirection: false,
    },
    {
      enabled: !!customerId && !useDummyData,
    }
  )

  // Use dummy data or parsed API response
  const rawData = useDummyData ? dummySystemActivityData : response?.data
  const parsedSystemActivity: ISystemActivity = parseCustomerSystemActivity(rawData)

  return {
    systemActivity: parsedSystemActivity,
    isLoading: useDummyData ? false : isFetching,
    filters,
    onSearch,
    onSortingChange,
    onPageChange,
  }
}
