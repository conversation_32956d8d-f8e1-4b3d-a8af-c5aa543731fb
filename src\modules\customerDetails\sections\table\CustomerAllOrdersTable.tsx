import {useEffect} from 'react'
import CustomerAllOrdersTableRow from './CustomerAllOrdersTableRow'

const CustomerAllOrdersTable = ({activeTab, ordersListData, isLoading, tableRef}: any) => {
  useEffect(() => {
    if (activeTab === 'consignment' && tableRef.current) {
      tableRef.current.scrollIntoView({behavior: 'smooth', block: 'center'})
    }
  })

  return (
    <div className='table-responsive position-relative' ref={tableRef}>
      <div className='table-loader-wrapper'>
        <table className='table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4 mb-15'>
          <thead className='table-row-bordered'>
            <tr className='fs-6 fw-semibold text-muted text-uppercase'>
              <th className='min-w-100px'>Order no.</th>
              <th className='min-w-120px'>Status</th>
              <th className='min-w-120px'>Amount</th>
              {activeTab === 'consignment' && <th className='min-w-150px'>Pending Amount</th>}
              <th className='min-w-100px'>Rewards</th>
              <th className='min-w-120px'>Date</th>
            </tr>
          </thead>
          <tbody>
            {ordersListData?.map((row: any) => (
              <CustomerAllOrdersTableRow row={row} key={row['id']} activeTab={activeTab} />
            ))}
          </tbody>
        </table>
        {isLoading && <Loading />}
      </div>
    </div>
  )
}

export default CustomerAllOrdersTable
