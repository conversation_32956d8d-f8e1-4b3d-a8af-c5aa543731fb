import {useContext} from 'react'
import {DynamicTable} from '../../../../components/DynamicTable'
import CustomerAllOrdersTableRow from './CustomerAllOrdersTableRow'
import {OrdersContext} from '../../context'

const CustomerAllOrdersTable = () => {
  const {orders, isLoading, onSortingChange, filters} = useContext(OrdersContext)

  const columns = [
    {
      key: 'id',
      label: 'ORDER',
      isSorted: true,
      headerStyle: 'min-w-100px',
    },
    {
      key: 'customer_first_name',
      label: 'CUSTOMER',
      isSorted: false,
      headerStyle: 'min-w-250px',
    },
    {
      key: 'date_created',
      label: 'DATE',
      isSorted: true,
      headerStyle: 'min-w-120px',
    },
    {
      key: 'status',
      label: 'STATUS',
      isSorted: false,
      headerStyle: 'min-w-100px',
      style: 'justify-content-center',
    },
    {
      key: 'payment_status',
      label: 'PAYMENT',
      isSorted: false,
      headerStyle: 'min-w-120px',
      style: 'justify-content-center',
    },
    {
      key: 'payment_method',
      label: 'METHOD',
      isSorted: false,
      headerStyle: 'min-w-200px',
    },
    {
      key: 'total_including_tax',
      label: 'TOTAL',
      isSorted: true,
      headerStyle: 'min-w-100px',
    },
    {
      key: 'items_total',
      label: 'ITEMS',
      isSorted: true,
      headerStyle: 'min-w-80px',
    },
    {
      key: 'ship_to',
      label: 'SHIP TO',
      isSorted: false,
      headerStyle: 'min-w-175px',
    },
    {
      key: 'channel',
      label: 'CHANNEL',
      isSorted: false,
      headerStyle: 'w-100px',
    },
  ]

  return (
    <DynamicTable
      id='customer-orders-table'
      data={orders}
      sortableColumns={columns}
      TableRow={CustomerAllOrdersTableRow}
      loading={isLoading}
      tableClass='table-row-dashed table-row-gray-300 align-middle gs-0 gy-4'
      noDataMessage='No orders found'
      onSortingChange={onSortingChange}
      filters={filters}
    />
  )
}

export default CustomerAllOrdersTable
