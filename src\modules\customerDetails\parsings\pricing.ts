import { IPricing } from '../interfaces'

export const parseCustomerPricing = (data: any[]): IPricing[] => {
  if (!Array.isArray(data)) return []
  
  return data.map(pricing => ({
    id: pricing.id || '',
    productId: pricing.product_id || pricing.productId || '',
    productName: pricing.product_name || pricing.productName || '',
    sku: pricing.sku || '',
    customerPrice: pricing.customer_price || pricing.customerPrice || 0,
    listPrice: pricing.list_price || pricing.listPrice || 0,
    discount: pricing.discount || 0,
    discountType: pricing.discount_type || pricing.discountType || '',
    effectiveDate: pricing.effective_date || pricing.effectiveDate || '',
    expirationDate: pricing.expiration_date || pricing.expirationDate || '',
    priceGroup: pricing.price_group || pricing.priceGroup || '',
    currency: pricing.currency || 'USD',
    isActive: pricing.is_active || pricing.isActive || true,
    createdDate: pricing.created_date || pricing.createdDate || '',
    lastModifiedDate: pricing.last_modified_date || pricing.lastModifiedDate || '',
  }))
}
