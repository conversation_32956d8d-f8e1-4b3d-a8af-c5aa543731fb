import useApi from '../../../services/useApi'

const useGetCustomerTags = (customerId: string, isEdit: boolean) => {
  const Api = useApi()
  const {data: response, isFetching} = Api.useGetQuery(
    `/customers/tags/${customerId}`,
    {queryId: `assigned-tags-${customerId}`},
    {enabled: !!customerId && isEdit, cacheTime: 0, staleTime: 0}
  )
  return {
    tags: response?.data || [],
    isLoading: !response && isFetching,
    isFetching,
  }
}

export default useGetCustomerTags
