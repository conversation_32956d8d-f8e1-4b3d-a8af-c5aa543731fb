import {Link} from 'react-router-dom'
import {getBadgeColor} from '../../../../utils/badge'
import Date from '../../../../components/Date/Date'
import {formatPrice} from '../../../../utils/common'
import Initials from '../../../../components/Initials'

const CustomerAllOrdersTableRow = ({row}: any) => {
  return (
    <tr>
      <td>
        <Link
          to={`/orders/all-orders/summary?id=${row?.orderNumber}`}
          className='text-dark text-hover-primary text-decoration-underline'
          target='_blank'
        >
          #{row?.orderNumber}
        </Link>
      </td>
      <td>
        <div className='d-flex align-items-center flex-grow-1 text-start cursor-pointer'>
          <div className='symbol symbol-circle symbol-35px me-3'>
            <Initials text={row?.customerFirstNameLetter} />
          </div>
          <div className='d-flex flex-column'>
            <div className='align-items-center text-dark fw-semibold'>{row?.customerName}</div>
            <div className='align-items-center text-muted'>{row?.customerEmail}</div>
          </div>
        </div>
      </td>
      <td>
        <Date date={row?.orderDate} />
      </td>
      <td className='text-center'>
        {row?.status ? (
          <span className={`badge ${getBadgeColor(row.status, 'light')} badge-lg`}>
            {row?.status}
          </span>
        ) : (
          '-'
        )}
      </td>
      <td className='text-center'>
        {row?.paymentStatus ? (
          <span
            className={`badge ${getBadgeColor(
              row.paymentStatus,
              'light'
            )} badge-lg text-capitalize`}
          >
            {row?.paymentStatus}
          </span>
        ) : (
          '-'
        )}
      </td>
      <td>{row?.orderPaymentMethod}</td>
      <td>{formatPrice(row?.totalAmount, false)}</td>
      <td>{row?.itemsCount}</td>
      <td>{row?.shipTo}</td>
      <td>{row?.orderChannel}</td>
    </tr>
  )
}

export default CustomerAllOrdersTableRow
