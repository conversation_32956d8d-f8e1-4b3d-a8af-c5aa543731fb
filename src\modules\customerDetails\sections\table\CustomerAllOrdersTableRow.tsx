import {Link} from 'react-router-dom'
import {getBadgeColor} from '../../../../utils/badge'
import Date from '../../../../components/Date/Date'

const CustomerAllOrdersTableRow = ({row, activeTab}: any) => {
  return (
    <tr>
      <td>
        <Link
          to={`/orders/all-orders/summary?id=${row?.id}`}
          className='text-dark text-hover-primary align-items-center'
          target='_blank'
        >
          {row?.id}
        </Link>
      </td>
      <td>
        <span className={`badge ${getBadgeColor(row['status'], 'light')} badge-lg`}>
          <div className='align-items-center'>{row['status']}</div>
        </span>
      </td>
      <td>{row['total']}</td>
      {activeTab === 'consignment' && <td>{row['pendingAmount']}</td>}
      <td>{row['rewards']}</td>
      <td>
        <Date date={row['created_at']} />
      </td>
    </tr>
  )
}

export default CustomerAllOrdersTableRow
