import {useContext, useState, useEffect} from 'react'
import {CustomerSpecificPriceContext} from '../context'
import Date from '../../../components/Date/Date'

const CustomerSpecificPriceTableRow = ({
  row,
  actionComponent,
  checkBoxComponent,
  hasWritePermission,
}: any) => {
  const {onUpdateCustomerSpecificPrice} = useContext(CustomerSpecificPriceContext)

  // Local state for editing price
  const [price, setPrice] = useState(row.price)
  const [margin, setMargin] = useState(row.margin)
  const [originalPrice, setOriginalPrice] = useState(row.price)
  const [isEditing, setIsEditing] = useState(false)

  // Sync local state if row.price changes externally
  useEffect(() => {
    setPrice(row.price)
    setOriginalPrice(row.price)
    setMargin(row.margin.toFixed(2))
  }, [row.price, row.margin])

  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.trim()

    if (value === '') {
      setPrice('')
      setMargin(0)
      return
    }

    // Validate input (only numbers, max 2 decimals)
    if (!/^\d*\.?\d{0,2}$/.test(value)) return

    const numericPrice = parseFloat(value)

    if (numericPrice < 0) {
      setPrice('')
    } else {
      setPrice(value)

      // Recalculate margin
      const cost = parseFloat(row.cost)
      if (cost) {
        const calculatedMargin =
          numericPrice === 0 ? 0 : Number((((numericPrice - cost) / cost) * 100).toFixed(2))
        setMargin(calculatedMargin)
      }
    }
  }

  const savePriceChange = () => {
    setIsEditing(false)

    // Reset if empty
    if (price === '') {
      setPrice(originalPrice)
      return
    }

    // Call API only if price has changed
    const formattedPrice = parseFloat(price)
    if (formattedPrice !== parseFloat(originalPrice)) {
      onUpdateCustomerSpecificPrice(row.product_id, formattedPrice, row.id)
      setOriginalPrice(price) // Update the original price after saving
    }
  }

  return (
    <tr>
      {checkBoxComponent && <td>{checkBoxComponent(row)}</td>}
      <td>{row.product_id}</td>
      <td>{row.product_name || '-'}</td>
      <td>{row.sku || '-'}</td>
      {hasWritePermission && <td>{row.cost ? `$${row.cost}` : '-'}</td>}
      <td className='text-center'>
        {isEditing && hasWritePermission ? (
          <>
            <input
              type='text'
              className='form-control w-75px text-center d-inline-block'
              value={price}
              onChange={handlePriceChange}
              onBlur={savePriceChange}
              autoFocus
            />
            {
              <div className={`text-nowrap mt-2 ${margin < 10 ? 'text-danger' : ''}`}>
                <span>
                  <strong>M:</strong>
                  <span>{margin}%</span>
                </span>
              </div>
            }
          </>
        ) : (
          <>
            <span
              className={`form-control w-75px text-center d-inline-block cursor-pointer ${
                actionComponent ? '' : 'bg-light'
              }`}
              onClick={() => setIsEditing(true)}
            >
              {price}
            </span>
            {
              <div className={`text-nowrap mt-2 ${margin < 10 ? 'text-danger' : ''}`}>
                <span>
                  <strong>M:</strong>
                  <span>{margin}%</span>
                </span>
              </div>
            }
          </>
        )}
      </td>
      <td>{row.distributor ? `$${row.distributor}` : '-'}</td>
      <td>{row.vip ? `$${row.vip}` : '-'}</td>
      <td>{row.updated_by || '-'}</td>
      <td>
        <Date date={row.updated_at} />
      </td>
      {actionComponent && <td>{actionComponent(row)}</td>}
    </tr>
  )
}

export default CustomerSpecificPriceTableRow
