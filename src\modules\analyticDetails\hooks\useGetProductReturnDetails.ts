import useApi from '../../../services/useApi'
import useFilters from '../../../hook/useFilters'
import {useState, useMemo} from 'react'

interface SortConfig {
  field: string
  direction: 'asc' | 'desc'
}

interface ProductReturnDetailsParams {
  variant_sku?: string
  parent_sku?: string
  isParent?: boolean
}

const useGetProductReturnDetails = (params: ProductReturnDetailsParams = {}) => {
  const Api = useApi()

  // State for sorting configuration
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null)

  const initialFilters = {
    variant_sku: params.variant_sku || '',
    parent_sku: params.parent_sku || '',
    returns_type: 'customer_returns',
  }

  const {setSingleFilter} = useFilters(initialFilters)

  const filters = {
    ...(params.isParent ? {parent_sku: params.parent_sku} : {variant_sku: params.variant_sku}),
    returns_type: 'customer_returns',
  }

  const {
    data: variantData,
    isFetching,
    refetch: variantRefetch,
  } = Api.useGetQuery(
    `/analytics/replenishment/zoho/returns/data`,
    {
      queryId: `product-variant-return-details-${params.variant_sku}`,
      filters: filters,
      isToast: false,
    },
    {
      enabled: !!params.variant_sku, // Only fetch when variant_sku is provided
    }
  )

  const {
    data: parentData,
    isFetching: isParentFetching,
    refetch: parentRefetch,
  } = Api.useGetQuery(
    `/analytics/replenishment/zoho/returns/parent/data`,
    {
      queryId: `product-return-details-${params.parent_sku}`,
      filters: filters,
      isToast: false,
    },
    {
      enabled: !!params.parent_sku, // Only fetch when parent_sku is provided
    }
  )

  const fetchProductVariantReturnDetails = (variantSku: string) => {
    setSingleFilter('variant_sku', variantSku)
    variantRefetch()
  }

  const fetchProductReturnDetails = (parentSku: string) => {
    setSingleFilter('parent_sku', parentSku)
    parentRefetch()
  }

  // Utility function to sort array of objects
  const sortData = (data: any[], field: string, direction: 'asc' | 'desc') => {
    if (!data || data.length === 0) return data

    return [...data].sort((a, b) => {
      let aValue = a[field]
      let bValue = b[field]

      // Handle null/undefined values
      if (aValue == null && bValue == null) return 0
      if (aValue == null) return direction === 'asc' ? 1 : -1
      if (bValue == null) return direction === 'asc' ? -1 : 1

      // Convert to numbers if they are numeric strings
      if (typeof aValue === 'string' && !isNaN(Number(aValue))) {
        aValue = Number(aValue)
      }
      if (typeof bValue === 'string' && !isNaN(Number(bValue))) {
        bValue = Number(bValue)
      }

      // Handle string comparison
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (aValue < bValue) {
        return direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return direction === 'asc' ? 1 : -1
      }
      return 0
    })
  }

  const onSortingChange = (key: string, value: string) => {
    console.log('07-15 key: ', key, value)

    // Parse the key to extract field and direction
    // key format: "field_name asc" or "field_name desc"
    const parts = key.trim().split(' ')
    const field = parts[0]
    const direction = parts[1] as 'asc' | 'desc'

    // Update sort configuration
    setSortConfig({ field, direction })
  }

  // Apply sorting to the data
  const sortedProductReturnDetails = useMemo(() => {
    const rawData = variantData?.data || parentData?.data || []

    if (!sortConfig || !rawData.length) {
      return rawData
    }

    return sortData(rawData, sortConfig.field, sortConfig.direction)
  }, [variantData?.data, parentData?.data, sortConfig])

  return {
    productReturnDetails: sortedProductReturnDetails,
    isLoading: isFetching || isParentFetching,
    filters,
    fetchProductReturnDetails,
    fetchProductVariantReturnDetails,
    parentRefetch,
    onSortingChange,
  }
}

export default useGetProductReturnDetails
