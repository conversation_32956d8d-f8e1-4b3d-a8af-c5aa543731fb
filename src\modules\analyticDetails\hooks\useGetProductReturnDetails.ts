import useApi from '../../../services/useApi'
import useFilters from '../../../hook/useFilters'
import {useState, useMemo} from 'react'

interface ProductReturnDetailsParams {
  variant_sku?: string
  parent_sku?: string
  isParent?: boolean
}

const useGetProductReturnDetails = (params: ProductReturnDetailsParams = {}) => {
  const Api = useApi()

  const initialFilters = {
    variant_sku: params.variant_sku || '',
    parent_sku: params.parent_sku || '',
    returns_type: 'customer_returns',
  }

  const {setSingleFilter} = useFilters(initialFilters)

  const filters = {
    ...(params.isParent ? {parent_sku: params.parent_sku} : {variant_sku: params.variant_sku}),
    returns_type: 'customer_returns',
  }

  const {
    data: variantData,
    isFetching,
    refetch: variantRefetch,
  } = Api.useGetQuery(
    `/analytics/replenishment/zoho/returns/data`,
    {
      queryId: `product-variant-return-details-${params.variant_sku}`,
      filters: filters,
      isToast: false,
    },
    {
      enabled: !!params.variant_sku, // Only fetch when variant_sku is provided
    }
  )

  const {
    data: parentData,
    isFetching: isParentFetching,
    refetch: parentRefetch,
  } = Api.useGetQuery(
    `/analytics/replenishment/zoho/returns/parent/data`,
    {
      queryId: `product-return-details-${params.parent_sku}`,
      filters: filters,
      isToast: false,
    },
    {
      enabled: !!params.parent_sku, // Only fetch when parent_sku is provided
    }
  )

  const fetchProductVariantReturnDetails = (variantSku: string) => {
    setSingleFilter('variant_sku', variantSku)
    variantRefetch()
  }

  const fetchProductReturnDetails = (parentSku: string) => {
    setSingleFilter('parent_sku', parentSku)
    parentRefetch()
  }

  const onSortingChange = (key: string, value: string) => {
    console.log('07-15 key: ', key,value);

  }

  return {
    productReturnDetails: variantData?.data || parentData?.data || [],
    isLoading: isFetching || isParentFetching,
    filters,
    fetchProductReturnDetails,
    fetchProductVariantReturnDetails,
    parentRefetch,
    onSortingChange,
  }
}

export default useGetProductReturnDetails
