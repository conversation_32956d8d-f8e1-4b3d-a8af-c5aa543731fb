import React from 'react'
import {KTSVG} from '../../../_metronic/helpers'

const AddressCard = ({
  title,
  icon,
  iconColor = 'primary',
  data,
}: {
  title: string
  icon: string
  iconColor?: string
  data: {label: string; value: string}[]
}) => (
  <div className='card card-flush border h-100'>
    <div className='card-header border-0 pt-6'>
      <div className='card-title'>
        <div className='d-flex align-items-center'>
          <div className={`symbol symbol-40px bg-light-${iconColor} me-3`}>
            <div className='symbol-label'>
              <KTSVG path={icon} className={`svg-icon-2 text-${iconColor}`} />
            </div>
          </div>
          <h3 className='fw-bold m-0'>{title}</h3>
        </div>
      </div>
    </div>
    <div className='card-body pt-0'>
      {data.map((item, index) => (
        <div key={index} className='mb-2'>
          <div className='text-muted fs-7 fw-semibold'>{item.label}:</div>
          <div className='fs-6 fw-bold text-gray-800'>{item.value}</div>
        </div>
      ))}
    </div>
  </div>
)

const AddressesSection = () => {
  const billingAddressData = [
    {label: 'Name', value: 'Acme Corporation'},
    {label: 'Company', value: 'Acme Corporation'},
    {label: 'Address Line 1', value: '123 Business Ave'},
    {label: 'Address Line 2', value: 'Suite 100'},
    {label: 'City', value: 'San Francisco'},
    {label: 'State', value: 'CA'},
    {label: 'Postal Code', value: '94105'},
    {label: 'Country', value: 'USA'},
  ]

  const shippingAddressData = [
    {label: 'Name', value: 'Acme Warehouse'},
    {label: 'Company', value: 'Acme Corporation'},
    {label: 'Address Line 1', value: '456 Warehouse Blvd'},
    {label: 'City', value: 'Oakland'},
    {label: 'State', value: 'CA'},
    {label: 'Postal Code', value: '94607'},
    {label: 'Country', value: 'USA'},
    {label: 'Delivery Instructions', value: 'Loading dock B'},
  ]

  return (
    <div className='row g-5 mb-5'>
      <div className='col-md-6'>
        <AddressCard
          title='Billing Address'
          icon='/media/icons/duotune/general/gen018.svg'
          iconColor='primary'
          data={billingAddressData}
        />
      </div>
      <div className='col-md-6'>
        <AddressCard
          title='Shipping Address 1'
          icon='/media/icons/duotune/maps/map004.svg'
          iconColor='success'
          data={shippingAddressData}
        />
      </div>
    </div>
  )
}

export default AddressesSection
