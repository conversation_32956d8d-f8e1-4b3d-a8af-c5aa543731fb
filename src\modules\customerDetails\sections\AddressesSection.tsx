import React, {useContext} from 'react'
import {KTSVG} from '../../../_metronic/helpers'
import {AddressesContext} from '../context'
import {IAddress} from '../interfaces'
import Loading from '../../loading'
import NoDataFound from '../../../components/NoDataFound'

const AddressCard = ({address, index}: {address: IAddress; index: number}) => (
  <div className='card card-flush border h-100'>
    <div className='card-header border-0 pt-6'>
      <div className='card-title'>
        <div className='d-flex align-items-center'>
          <div className='symbol symbol-40px bg-light-primary me-3'>
            <div className='symbol-label'>
              <KTSVG
                path='/media/icons/duotune/general/gen018.svg'
                className='svg-icon-2 text-primary'
              />
            </div>
          </div>
          <h3 className='m-0'>Address {index + 1}</h3>
        </div>
      </div>
    </div>
    <div className='card-body pt-0'>
      <div className='mb-3 d-flex gap-3'>
        <div className='fw-semibold'>Name:</div>
        <div className='text-gray-700'>
          {address.firstName} {address.lastName}
        </div>
      </div>

      {address.company && (
        <div className='mb-3 d-flex gap-3'>
          <div className='fw-semibold'>Company:</div>
          <div className='text-gray-700'>{address.company}</div>
        </div>
      )}

      <div className='mb-3 d-flex gap-3'>
        <div className='fw-semibold'>Address line 1:</div>
        <div className='text-gray-700'>
          {address.addressLine1}
        </div>
      </div>

      <div className='mb-3 d-flex gap-3'>
        <div className='fw-semibold'>Address line 2:</div>
        <div className='text-gray-700'>
          {address.addressLine2}
        </div>
      </div>

      <div className='mb-3 d-flex gap-3'>
        <div className='fw-semibold'>City</div>
        <div className='text-gray-700'>
          {address.city}
        </div>
      </div>

      <div className='mb-3 d-flex gap-3'>
        <div className='fw-semibold'>State:</div>
        <div className='text-gray-700'>
          {address.state}
        </div>
      </div>

      <div className='mb-3 d-flex gap-3'>
        <div className='fw-semibold'>Postal Code:</div>
        <div className='text-gray-700'>
          {address.zipCode}
        </div>
      </div>

      <div className='mb-3 d-flex gap-3'>
        <div className='fw-semibold'>Country:</div>
        <div className='text-gray-700'>{address.country}</div>
      </div>
    </div>
  </div>
)

const AddressesSection = () => {
  const {addresses, isLoading} = useContext(AddressesContext)

  return (
    <div className='row g-5 mb-5 position-relative'>
      {isLoading && <Loading />}
      {!isLoading && !addresses.length && <NoDataFound colspan={3} />}
      {addresses.map((address: IAddress, index: number) => (
        <div key={address.id} className='col-md-6 col-xl-4'>
          <AddressCard address={address} index={index} />
        </div>
      ))}
    </div>
  )
}

export default AddressesSection
