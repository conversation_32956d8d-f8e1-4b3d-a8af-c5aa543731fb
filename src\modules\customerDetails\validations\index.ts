import * as yup from 'yup'

export const validationSchema = yup.object({
  tags: yup
    .array()
    .of(
      yup
        .string()
        .test('is-not-empty', 'Please Enter Valid Tag', (value: any) => value.trim() !== '')
    ),
})

export const AddPriceRuleSchema = yup.object().shape({
  parent_sku: yup.string().required('Product ID is required'),
  price: yup
    .number()
    .typeError('Price must be a number')
    .positive('Price must be a positive value')
    .test('decimal', 'Only up to two decimal places allowed', (value) =>
      value ? /^\d+(\.\d{1,2})?$/.test(value.toString()) : true
    )
    .required('Price is required'),
})
