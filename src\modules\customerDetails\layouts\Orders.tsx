import React from 'react'
import OrdersSection from '../sections/OrdersSection'
import useGetCustomerOrders from '../hooks/useGetCustomerOrders'
import {OrdersContext} from '../context'

const Orders = () => {
  const {orders, isLoading} = useGetCustomerOrders()
  const contextValue = {orders, isLoading}
  return (
    <OrdersContext.Provider value={contextValue}>
      <OrdersSection />
    </OrdersContext.Provider>
  )
}

export default Orders
