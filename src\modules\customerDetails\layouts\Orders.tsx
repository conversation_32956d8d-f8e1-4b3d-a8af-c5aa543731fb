import useGetCustomerOrders from '../hooks/useGetCustomerOrders'
import {OrdersContext} from '../context'
import CustomerAllOrdersTable from '../sections/table/CustomerAllOrdersTable'
import {Pagination} from '../../../utils/pagination'

const Orders = () => {
  const {orders, isLoading, pagination, onPageChange, onSortingChange, filters} = useGetCustomerOrders()
  const contextValue = {orders, isLoading, onSortingChange, filters}
  return (
    <OrdersContext.Provider value={contextValue}>
      <CustomerAllOrdersTable />
      <Pagination pagination={pagination} onPageChange={onPageChange} />
    </OrdersContext.Provider>
  )
}

export default Orders
