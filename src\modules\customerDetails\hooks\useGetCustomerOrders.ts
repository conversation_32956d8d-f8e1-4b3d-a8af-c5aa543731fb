import useFilters from '../../../hook/useFilters'
import useApi from '../../../services/useApi'
import { parseCustomerOrders } from '../parsings/orders'
import { IOrder } from '../interfaces'

// Dummy data for development
const dummyOrdersData = [
  {
    id: '1',
    order_number: 'ORD-003',
    order_date: '2024-01-15',
    status: 'Completed',
    total_amount: 2850,
    currency: 'USD',
    payment_method: 'Credit Card',
    shipping_address: '123 Main Street, New York, NY 10001',
    billing_address: '123 Main Street, New York, NY 10001',
    items: [
      {
        id: '1',
        product_id: 'PROD-001',
        product_name: 'Widget A',
        sku: 'WID-A-001',
        quantity: 10,
        unit_price: 285,
        total_price: 2850,
      },
    ],
    created_date: '2024-01-15',
    last_modified_date: '2024-01-20',
  },
]

export default function useGetCustomerOrders(customerId?: string) {
  const initialFilters = {
    page: 1,
    limit: 20,
  }
  const Api = useApi()
  const {filters, onSearch, onSortingChange, onPageChange} = useFilters(initialFilters)

  // For development, return dummy data instead of API call
  const useDummyData = true // Set to false when API is ready

  const {data: response, isFetching} = Api.useGetQuery(
    customerId && !useDummyData ? `/customers/${customerId}/orders` : '',
    {
      queryId: customerId ? `customer-orders-${customerId}` : undefined,
      filters,
      isErrorPageRedirection: false,
    },
    {
      enabled: !!customerId && !useDummyData,
    }
  )

  // Use dummy data or parsed API response
  const rawData = useDummyData ? dummyOrdersData : response?.data
  const parsedOrders: IOrder[] = parseCustomerOrders(rawData)

  return {
    orders: parsedOrders,
    isLoading: useDummyData ? false : isFetching,
    filters,
    onSearch,
    onSortingChange,
    onPageChange,
  }
}
