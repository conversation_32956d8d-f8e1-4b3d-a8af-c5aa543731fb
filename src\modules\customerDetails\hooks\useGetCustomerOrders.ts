import useFilters from '../../../hook/useFilters'
import useApi from '../../../services/useApi'
import { parseCustomerOrders } from '../parsings/orders'
import {useParams} from 'react-router-dom'

export default function useGetCustomerOrders() {
  const {id: customerId} = useParams<{id: string}>()
  const initialFilters = {
    page: 1,
    limit: 20,
  }
  const Api = useApi()
  const {filters, onSearch, onSortingChange, onPageChange} = useFilters(initialFilters,{
    sortType: 'number',
  })

  const {data: response, isFetching} = Api.useGetQuery(
    `/customers/${customerId}/salesforce-orders`,
    {
      queryId: customerId ? `customer-orders-${customerId}` : undefined,
      filters,
      isErrorPageRedirection: false,
    },
    {
      enabled: !!customerId,
    }
  )

  return {
    orders: parseCustomerOrders(response?.data),
    pagination: response?.pagination,
    isLoading: isFetching,
    filters,
    onSearch,
    onSortingChange,
    onPageChange,
  }
}
