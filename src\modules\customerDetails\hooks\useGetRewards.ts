import {useEffect} from 'react'
import {useQuery} from 'react-query'
import useFilters from '../../../hook/useFilters'
import Api from '../../../services/Api'

export const getRewards = (url: string, filters: any) => {
  return Api.get(`${url}`, filters).then((d: any) => {
    return d
  })
}

const initialFilters = {
  sort_by: 'created_at',
  sort_order: 'desc',
  page: 1,
  limit: 20,
  filterBy: '',
}

export const useGetRewards = (customer_id: any) => {
  const {filters, setSingleFilter, setMultipleFilters} = useFilters(initialFilters)

  const {
    data: response,
    refetch,
    isFetching,
  } = useQuery(`get-Rewards-History`, () => {
    return getRewards(`/customers/bc/${customer_id}/loyalty/history`, filters)
  })

  useEffect(() => {
    refetch()
  }, [filters, refetch])

  const onSearch = (searchValue: string, searchKey: string) => {
    setMultipleFilters({
      filterValue: searchValue,
      filterBy: searchKey,
    })
  }

  const onPageChange = (page_number: number) => {
    setSingleFilter('page', page_number)
  }
  const onSortingChange = (key: string, value: string) => {
    setMultipleFilters({
      sort_by: key,
      sort_order: value,
    })
  }

  return {
    rewards: response?.['data']?.['data'] || [],
    filters,
    pagination: response?.['data']?.['meta']?.['pagination'] || {},
    refetch,
    onSearch,
    onPageChange,
    onSortingChange,
    isLoading: isFetching,
  }
}
