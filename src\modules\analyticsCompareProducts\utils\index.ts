// Interface for comparison row structure
export interface ComparisonRow {
  section: string
  label: string
  key: string
  isSection?: boolean
}

// Define the comparison structure following Flipkart-style layout
export const COMPARISON_STRUCTURE: ComparisonRow[] = [
  // Product Info Section
  {section: 'Product Info', label: 'Product Image', key: 'product_image'},
  {section: 'Product Info', label: 'Product Name', key: 'product_name'},
  {section: 'Product Info', label: 'Product SKU', key: 'product_sku'},

  // Orders Section
  {section: 'Orders', label: 'Total Orders Placed', key: 'orders.total'},

  // Quantity Sold Section
  {
    section: 'Quantity Sold',
    label: 'Total Quantity Sold',
    key: 'quantity_sold.total',
  },

  // Earnings Section
  {section: 'Earnings', label: 'Total Earnings', key: 'earnings.total'},

  // Total Sold Quantity Section
  {
    section: 'Total Sold Quantity',
    label: 'Total Quantity',
    key: 'total_sold_quantity',
  },
  {section: 'Total Sold Quantity', label: 'Monthly Sales (Last 6 Months)', key: 'monthly_sales'},

  // Sales by State Section
  {section: 'Sales by State', label: 'Quantity Sold per State', key: 'sales_by_state'},

  // Inventory Info Section
  {
    section: 'Inventory Info',
    label: 'Available Quantity',
    key: 'inventory_info.available_quantity',
  },
  {section: 'Inventory Info', label: 'Blocked Quantity', key: 'inventory_info.blocked_quantity'},
  {section: 'Inventory Info', label: 'Incoming Quantity', key: 'inventory_info.incoming_quantity'},
]

// Helper to get nested value from object
export const getValue = (obj: any, path: string) => {
  return path
    .split('.')
    .reduce((acc, part) => (acc && acc[part] !== undefined ? acc[part] : '-'), obj)
}

// Build all comparison rows with dynamic customer type, month, and state rows
export const buildComparisonRows = (customerTypes: any, months: any, states: any): ComparisonRow[] => {
  const rows: ComparisonRow[] = []

  // Add basic structure rows and inject dynamic rows where needed
  COMPARISON_STRUCTURE.forEach((row) => {
    // Skip the generic monthly_sales and sales_by_state rows
    if (row.key === 'monthly_sales' || row.key === 'sales_by_state') {
      return
    }

    rows.push(row)

    // Add customer type sub-rows after main category rows
    if (row.key === 'orders.total') {
      Object.entries(customerTypes).forEach(([key, label]) => {
        rows.push({
          section: 'Orders',
          label: `${String(label)[0].toUpperCase() + String(label).slice(1).replace(/_/g, ' ')}`,
          key: `orders.${key}`,
        })
      })
    } else if (row.key === 'quantity_sold.total') {
      Object.entries(customerTypes).forEach(([key, label]) => {
        rows.push({
          section: 'Quantity Sold',
          label: `${String(label)[0].toUpperCase() + String(label).slice(1).replace(/_/g, ' ')}`,
          key: `quantity_sold.${key}`,
        })
      })
    } else if (row.key === 'earnings.total') {
      Object.entries(customerTypes).forEach(([key, label]) => {
        rows.push({
          section: 'Earnings',
          label: `${String(label)[0].toUpperCase() + String(label).slice(1).replace(/_/g, ' ')}`,
          key: `earnings.${key}`,
        })
      })
    } else if (row.key === 'total_sold_quantity') {
      // Add individual month rows after Total Quantity (All Variants)
      Object.entries(months).forEach(([monthKey, monthLabel]) => {
        rows.push({
          section: 'Total Sold Quantity',
          label: String(monthLabel),
          key: `monthly_sales.${monthKey}`,
        })
      })
    }
  })

  // Add Sales by State section with individual state rows
  Object.entries(states).forEach(([stateKey, stateLabel]) => {
    rows.push({
      section: 'Sales by State',
      label: String(stateLabel),
      key: `sales_by_state.${stateKey}`,
    })
  })

  return rows
}

export const dummyCompareProductsData = {
  data: [
    {
      product_id: 'P123',
      product_name: 'Product 1',
      product_sku: 'SKU123',
      product_image: 'https://via.placeholder.com/150x150/007bff/ffffff?text=P1',
      orders: {
        total_orders: 120,
        customer_type_1: 50,
        customer_type_2: 40,
        customer_type_3: 30,
        customer_type_4: '-',
        customer_type_5: '-',
      },
      quantity_sold: {
        total_quantity_sold: 600,
        customer_type_1: 250,
        customer_type_2: 200,
        customer_type_3: 150,
        customer_type_4: '-',
        customer_type_5: '-',
      },
      earnings: {
        total_earnings: 12000.0,
        customer_type_1: 5000.0,
        customer_type_2: 4000.0,
        customer_type_3: 3000.0,
        customer_type_4: '-',
        customer_type_5: '-',
      },
      total_quantity_sold: 600,
      monthly_sales: {
        month_1: 120,
        month_2: 100,
        month_3: 80,
        month_4: 90,
        month_5: 110,
        month_6: 100,
        month_7: '-',
      },
      sales_by_state: {
        state_1: 150,
        state_2: 120,
        state_3: 80,
        state_4: 50,
        state_5: '-',
        state_6: '-',
      },
      inventory_info: {
        available_quantity: 300,
        blocked_quantity: 20,
        incoming_quantity: 50,
      },
    },
    {
      product_id: 'P456',
      product_name: 'Product 2',
      product_sku: 'SKU456',
      product_image: 'https://via.placeholder.com/150x150/28a745/ffffff?text=P2',
      orders: {
        total_orders: 95,
        customer_type_1: 35,
        customer_type_2: 30,
        customer_type_3: 30,
        customer_type_4: '-',
        customer_type_5: '-',
      },
      quantity_sold: {
        total_quantity_sold: 480,
        customer_type_1: 180,
        customer_type_2: 150,
        customer_type_3: 150,
        customer_type_4: '-',
        customer_type_5: '-',
      },
      earnings: {
        total_earnings: 9600.0,
        customer_type_1: 3600.0,
        customer_type_2: 3000.0,
        customer_type_3: 3000.0,
        customer_type_4: '-',
        customer_type_5: '-',
      },
      total_quantity_sold: 480,
      monthly_sales: {
        month_1: 95,
        month_2: 85,
        month_3: 70,
        month_4: 75,
        month_5: 90,
        month_6: 85,
        month_7: '-',
      },
      sales_by_state: {
        state_1: 120,
        state_2: 100,
        state_3: 70,
        state_4: 40,
        state_5: '-',
        state_6: '-',
      },
      inventory_info: {
        available_quantity: 250,
        blocked_quantity: 15,
        incoming_quantity: 40,
      },
    },
    {
      product_id: 'P789',
      product_name: 'Product 3',
      product_sku: 'SKU789',
      product_image: 'https://via.placeholder.com/150x150/dc3545/ffffff?text=P3',
      orders: {
        total_orders: 150,
        customer_type_1: 60,
        customer_type_2: 50,
        customer_type_3: 40,
        customer_type_4: '-',
        customer_type_5: '-',
      },
      quantity_sold: {
        total_quantity_sold: 750,
        customer_type_1: 300,
        customer_type_2: 250,
        customer_type_3: 200,
        customer_type_4: '-',
        customer_type_5: '-',
      },
      earnings: {
        total_earnings: 15000.0,
        customer_type_1: 6000.0,
        customer_type_2: 5000.0,
        customer_type_3: 4000.0,
        customer_type_4: '-',
        customer_type_5: '-',
      },
      total_quantity_sold: 750,
      monthly_sales: {
        month_1: 150,
        month_2: 130,
        month_3: 110,
        month_4: 120,
        month_5: 140,
        month_6: 130,
        month_7: '-',
      },
      sales_by_state: {
        state_1: 180,
        state_2: 150,
        state_3: 100,
        state_4: 70,
        state_5: '-',
        state_6: '-',
      },
      inventory_info: {
        available_quantity: 400,
        blocked_quantity: 25,
        incoming_quantity: 60,
      },
    },
  ],
  meta: {
    month_rows: {
      month_1: 'Jul',
      month_2: 'Jun',
      month_3: 'May',
      month_4: 'Apr',
      month_5: 'Mar',
      month_6: 'Feb',
      month_7: 'Jan',
    },
    states: {
      state_1: 'CA',
      state_2: 'TX',
      state_3: 'NY',
      state_4: 'FL',
      state_5: 'IL',
      state_6: 'GA',
    },
    customer_types: {
      customer_type_1: 'distributor',
      customer_type_2: 'retailer',
      customer_type_3: 'chain_store_retailer',
      customer_type_4: 'MVD',
      customer_type_5: 'TCD',
    },
  },
}
