export const dummyCompareProductsData = {
  data: [
    {
      product_id: 'P123',
      product_name: 'Product 1',
      product_sku: 'SKU123',
      product_image: 'https://example.com/product1.png',
      orders: {
        total_orders: 120,
        customer_type_1: 50,
        customer_type_2: 40,
        customer_type_3: 30,
        customer_type_4: '-',
        customer_type_5: '-',
      },
      quantity_sold: {
        total_quantity_sold: 600,
        customer_type_1: 250,
        customer_type_2: 200,
        customer_type_3: 150,
        customer_type_4: '-',
        customer_type_5: '-',
      },
      earnings: {
        total_earnings: 12000.0,
        customer_type_1: 5000.0,
        customer_type_2: 4000.0,
        customer_type_3: 3000.0,
        customer_type_4: '-',
        customer_type_5: '-',
      },
      total_quantity_sold: 600,
      monthly_sales: {
        month_1: 120,
        month_2: 100,
        month_3: 80,
        month_4: 90,
        month_5: 110,
        month_6: 100,
        month_7: '-',
      },
      sales_by_state: {
        state_1: 150,
        state_2: 120,
        state_3: 80,
        state_4: 50,
        state_5: '-',
        state_6: '-',
      },
      inventory_info: {
        available_quantity: 300,
        blocked_quantity: 20,
        incoming_quantity: 50,
      },
    },
    // Add more products as needed for testing
  ],
  meta: {
    month_rows: {
      month_1: 'Jul',
      month_2: 'Jun',
      month_3: 'May',
      month_4: 'Apr',
      month_5: 'Mar',
      month_6: 'Feb',
      month_7: 'Jan',
    },
    states: {
      state_1: 'CA',
      state_2: 'TX',
      state_3: 'NY',
      state_4: 'FL',
      state_5: 'IL',
      state_6: 'GA',
    },
    customer_types: {
      customer_type_1: 'distributor',
      customer_type_2: 'retailer',
      customer_type_3: 'chain_store_retailer',
      customer_type_4: 'MVD',
      customer_type_5: 'TCD',
    },
  },
}
