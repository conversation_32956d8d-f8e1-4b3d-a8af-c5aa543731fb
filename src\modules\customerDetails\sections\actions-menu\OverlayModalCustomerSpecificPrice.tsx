import {useContext, useState} from 'react'
import {CustomerSpecificPriceContext} from '../../context'
import {ConfirmationModal} from '../../../../components/ConfirmationModal'
import {CustomerSpecificPriceDropdown} from './CustomerSpecificPriceDropdown'
import AddPriceRuleModal from '../models/AddPriceRuleModal'
import OverlayComponent from '../../../../_metronic/layout/components/Popover'

const OverlayModalCustomerSpecificPrice = ({row}: any) => {
  const {onDeleteCustomerSpecificPrice, isOperationLoading, isLoading, setCheckedRows} = useContext(
    CustomerSpecificPriceContext
  )
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)

  const handleDeleteConfirm = async () => {
    const res: any = onDeleteCustomerSpecificPrice({ids: row?.id})
    if (res?.status === 200) {
      setShowDeleteModal(false)
      setCheckedRows([])
    }
  }

  return (
    <>
      <OverlayComponent
        btnIcon={<i className='las la-ellipsis-h fs-2x'></i>}
        children={<CustomerSpecificPriceDropdown setShowDeleteModal={setShowDeleteModal} />}
      />
      {showEditModal && (
        <AddPriceRuleModal show={showEditModal} onClose={() => setShowEditModal(false)} />
      )}
      {showDeleteModal && (
        <ConfirmationModal
          uniqueID={row?.id}
          show={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onAction={handleDeleteConfirm}
          disableAction={isOperationLoading}
          isOperationLoading={isOperationLoading}
          isDataLoading={isLoading}
        />
      )}
    </>
  )
}

export default OverlayModalCustomerSpecificPrice
