/* eslint-disable react-hooks/exhaustive-deps */
import {DropdownCustomersListing} from '../dropdown/DropdownCustomersListing'
import {Link} from 'react-router-dom'
import parseCustomers from '../../services/customers'
import OverlayComponent from '../../../../_metronic/layout/components/Popover'
import Initials from '../../../../components/Initials'
import {formatPrice} from '../../../../utils/common'
import Date from '../../../../components/Date/Date'
import {CustomersContext} from '../../contexts'
import {useContext, useState, useCallback, useEffect} from 'react'
import usePermission from '../../../../hook/usePermission'
import {useAppSelector} from '../../../../redux/useTypedSelector'
// import useGetCustomerGroup from '../../hooks/useGetCustomerGroup'

const TableRow = ({row}: any) => {
  const customer = parseCustomers(row)
  // const customergroups = useGetCustomerGroup()
  const {onCheckShowInvoice} = useContext(CustomersContext)
  const [isUpdating, setIsUpdating] = useState(false)
  const [isUpdatingLoyaltyPoint, setIsUpdatingLoyaltyPoint] = useState(false)
  const [is_invoice_visible, setIsInvoiceVisible] = useState(customer['is_invoice_visible'])
  const [is_loyalty_point_enable, setIsLoyaltyPointEnable] = useState(
    customer['is_loyalty_point_enable']
  )
  const {hasPermission} = usePermission()
  const {user} = useAppSelector((state) => state.auth)

  useEffect(() => {
    setIsInvoiceVisible(customer['is_invoice_visible'])
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [customer['is_invoice_visible']])

  // Helper function to convert select value to boolean/null for API
  const convertToApiValue = (value: string): boolean | null => {
    if (value === 'true') return true
    if (value === 'false') return false
    return null // for empty string or 'None'
  }

  // Handles: null | true | false | "true" | "false"
  const convertToSelectValue = (value: any): string => {
    if (value === true || value === 'true') return 'true'
    if (value === false || value === 'false') return 'false'
    return '' // for null, undefined, or empty string
  }

  const handleCheckShowInvoice = useCallback(
    async (id: any, selectedValue: string) => {
      // Prevent duplicate calls
      if (isUpdating || String(is_invoice_visible) === selectedValue) {
        return
      }

      // Immediately update UI state
      setIsInvoiceVisible(selectedValue)
      setIsUpdating(true)

      try {
        const data = {
          is_invoice_visible: convertToApiValue(selectedValue),
        }
        await onCheckShowInvoice(id, data)
      } catch (error) {
        // Revert state on error
        setIsInvoiceVisible(convertToSelectValue(customer['is_invoice_visible']))
      } finally {
        setIsUpdating(false)
      }
    },
    [isUpdating, is_invoice_visible, onCheckShowInvoice, customer]
  )

  const handleCheckShowLoyaltyPoint = useCallback(
    async (id: any, selectedValue: string) => {
      // Prevent duplicate calls
      if (isUpdatingLoyaltyPoint || String(is_loyalty_point_enable) === selectedValue) {
        return
      }

      // Immediately update UI state
      setIsLoyaltyPointEnable(selectedValue)
      setIsUpdatingLoyaltyPoint(true)

      try {
        const data = {
          is_loyalty_point_enable: convertToApiValue(selectedValue), // Send as boolean/null
        }
        await onCheckShowInvoice(id, data)
      } catch (error) {
        // Revert state on error
        setIsLoyaltyPointEnable(convertToSelectValue(customer['is_loyalty_point_enable']))
      } finally {
        setIsUpdatingLoyaltyPoint(false)
      }
    },
    [isUpdatingLoyaltyPoint, is_loyalty_point_enable, onCheckShowInvoice, customer]
  )

  return (
    <tr>
      <td>
        <Link
          to={`/customers/details/${row['id']}`}
          state={{
            id: row['id'],
          }}
          target='_blank'
          className='d-flex align-items-center flex-grow-1 text-start cursor-pointer'
        >
          <div className='symbol symbol-circle symbol-35px me-3'>
            <Initials text={customer['customerFirstNameLetter']} />
          </div>
          <div className='d-flex flex-column'>
            <div className='align-items-center text-dark fw-semibold'>{customer['name']}</div>
            <div className='align-items-center text-muted'>{customer['email']}</div>
          </div>
        </Link>
      </td>
      <td>
        <div>{customer['company_name']}</div>
      </td>
      <td>
        <div>{customer['phone']}</div>
      </td>
      <td>
        <div>{customer['customer_rep_name']}</div>
      </td>
      <td>
        <div>{customer['customer_group_name']}</div>
      </td>
      <td>
        <div>{formatPrice(customer['storecredit'], true)}</div>
      </td>
      <td>
        <div>{customer['orders']}</div>
      </td>
      <td>
        <div>{formatPrice(customer['orderAmountDue']?.toString(), true)}</div>
      </td>
      <td className='text-start'>
        <div>
          {customer['creditLimit'] ? formatPrice(customer['creditLimit']?.toString(), true) : '-'}
        </div>
      </td>
      <td>
        <div>{customer['paymentTerm']}</div>
      </td>
      <td>
        <Date date={row['date_created']} />
      </td>
      <td>
        <Date date={customer['lastOrderDate']} />
      </td>
      {/* <td>
        <div className='align-items-center'>{'-'}</div>
      </td> */}
      {user?.is_owner && (
        <>
          <td>
            <div className='d-flex align-items-center'>
              <select
                value={String(is_invoice_visible)}
                onChange={(e) => handleCheckShowInvoice(row['id'], e.target.value)}
                className='form-select'
                disabled={isUpdating || !hasPermission('customers_all customer', 'write')}
              >
                <option value=''>None</option>
                <option value='true'>Show Print Invoice</option>
                <option value='false'>Hide Print Invoice</option>
              </select>
            </div>
          </td>
          <td>
            <div className='d-flex align-items-center'>
              <select
                value={String(is_loyalty_point_enable)}
                onChange={(e) => handleCheckShowLoyaltyPoint(row['id'], e.target.value)}
                className='form-select'
                disabled={
                  isUpdatingLoyaltyPoint || !hasPermission('customers_all customer', 'write')
                }
              >
                <option value=''>None</option>
                <option value='true'>Active Loyalty Point</option>
                <option value='false'>Inactive Loyalty Point</option>
              </select>
            </div>
          </td>
        </>
      )}
      <td>
        <div className='d-flex justify-content-center align-items-center flex-shrink-0'>
          <OverlayComponent
            btnIcon={<i className='las la-ellipsis-h fs-2x'></i>}
            children={<DropdownCustomersListing customerId={row?.['id']} custom_url={''} />}
          />
        </div>
      </td>
    </tr>
  )
}

export default TableRow
