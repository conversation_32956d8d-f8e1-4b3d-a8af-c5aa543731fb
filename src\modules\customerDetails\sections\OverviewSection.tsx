import React from 'react'
import {KTSVG} from '../../../_metronic/helpers'
import {IOverview} from '../interfaces'
import Loading from '../../loading'

interface OverviewSectionProps {
  overview: IOverview
  isLoading: boolean
}

const OverviewSection: React.FC<OverviewSectionProps> = ({overview, isLoading}) => {
  return (
    <div className='row g-5 mb-5 position-relative'>
      {isLoading && <Loading />}
      
      {/* Top summary cards */}
      <div className='col-md-3'>
        <div className='card  border h-100'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center'>
              <div className='flex-grow-1'>
                <div className='text-muted fs-7 fw-semibold mb-1'>Total Orders</div>
                <div className='fw-bold fs-1 text-gray-800'>{overview.totalOrders || 0}</div>
              </div>
              <div className='symbol symbol-50px bg-info bg-opacity-10'>
                <div className='symbol-label'>
                  <KTSVG
                    path='/media/icons/duotune/ecommerce/ecm002.svg'
                    className='svg-icon-2 text-info'
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className='col-md-3'>
        <div className='card border h-100'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center'>
              <div className='flex-grow-1'>
                <div className='text-muted fs-7 fw-semibold mb-1'>Total Spent</div>
                <div className='fw-bold fs-1 text-gray-800'>{overview.totalRevenue || '-'}</div>
              </div>
              <div className='symbol symbol-50px bg-primary bg-opacity-10'>
                <div className='symbol-label'>
                  <KTSVG
                    path='/media/icons/duotune/finance/fin010.svg'
                    className='svg-icon-2 text-primary'
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className='col-md-3'>
        <div className='card  border h-100'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center'>
              <div className='flex-grow-1'>
                <div className='text-muted fs-7 fw-semibold mb-1'>Avg Order Value</div>
                <div className='fw-bold fs-1 text-gray-800'>{overview.avgOrderValue || '-'}</div>
              </div>
              <div className='symbol symbol-50px bg-warning bg-opacity-10'>
                <div className='symbol-label'>
                  <KTSVG
                    path='/media/icons/duotune/general/gen025.svg'
                    className='svg-icon-2 text-warning'
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className='col-md-3'>
        <div className='card  border h-100'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center'>
              <div className='flex-grow-1'>
                <div className='text-muted fs-7 fw-semibold mb-1'>Store Credit</div>
                <div className='fw-bold fs-1 text-gray-800'>{overview.storeCredit || '-'}</div>
              </div>
              <div className='symbol symbol-50px bg-success bg-opacity-10'>
                <div className='symbol-label'>
                  <KTSVG
                    path='/media/icons/duotune/general/gen024.svg'
                    className='svg-icon-2 text-success'
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Details cards */}
      <div className='col-md-4'>
        <div className='card border  h-100 mt-5'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-4'>
              <KTSVG
                path='/media/icons/duotune/general/gen014.svg'
                className='svg-icon-2 text-primary me-2'
              />
              <h5 className='fw-bold m-0 text-gray-800'>Customer Info</h5>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <KTSVG
                  path='/media/icons/duotune/general/gen043.svg'
                  className='svg-icon-5 text-muted me-2'
                />
                <span className='text-muted fs-7'>Status:</span>
                <span className='badge badge-success ms-2'>{overview.status || 'Active'}</span>
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <KTSVG
                  path='/media/icons/duotune/general/gen014.svg'
                  className='svg-icon-5 text-muted me-2'
                />
                <span className='text-muted fs-7'>Customer Type:</span>
                <span className='text-gray-800 fw-semibold ms-2'>
                  {overview.customerType || '-'}
                </span>
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <KTSVG
                  path='/media/icons/duotune/general/gen014.svg'
                  className='svg-icon-5 text-muted me-2'
                />
                <span className='text-muted fs-7'>Tier:</span>
                <span className='text-gray-800 fw-semibold ms-2'>{overview.tier || '-'}</span>
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <KTSVG
                  path='/media/icons/duotune/general/gen007.svg'
                  className='svg-icon-5 text-muted me-2'
                />
                <span className='text-muted fs-7'>Tags:</span>
                <span className='text-gray-800 fw-semibold ms-2'>{overview.tags || '-'}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className='col-md-4'>
        <div className='card border  h-100 mt-5'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-4'>
              <KTSVG
                path='/media/icons/duotune/communication/com014.svg'
                className='svg-icon-2 text-primary me-2'
              />
              <h5 className='fw-bold m-0 text-gray-800'>Contact</h5>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <KTSVG
                  path='/media/icons/duotune/communication/com011.svg'
                  className='svg-icon-5 text-muted me-2'
                />
                <span className='text-muted fs-7'>Email:</span>
                <span className='text-gray-800 fw-semibold ms-2'>{overview.email || '-'}</span>
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <KTSVG
                  path='/media/icons/duotune/communication/com005.svg'
                  className='svg-icon-5 text-muted me-2'
                />
                <span className='text-muted fs-7'>Phone:</span>
                <span className='text-gray-800 fw-semibold ms-2'>{overview.phone || '-'}</span>
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <KTSVG
                  path='/media/icons/duotune/communication/com013.svg'
                  className='svg-icon-5 text-muted me-2'
                />
                <span className='text-muted fs-7'>Website:</span>
                <span className='text-gray-800 fw-semibold ms-2'>{overview.website || '-'}</span>
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <KTSVG
                  path='/media/icons/duotune/communication/com006.svg'
                  className='svg-icon-5 text-muted me-2'
                />
                <span className='text-muted fs-7'>Account Manager:</span>
                <span className='text-gray-800 fw-semibold ms-2'>
                  {overview.accountManager || '-'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className='col-md-4'>
        <div className='card border  h-100 mt-5'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-4'>
              <KTSVG
                path='/media/icons/duotune/finance/fin010.svg'
                className='svg-icon-2 text-primary me-2'
              />
              <h5 className='fw-bold m-0 text-gray-800'>Financial</h5>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <KTSVG
                  path='/media/icons/duotune/finance/fin006.svg'
                  className='svg-icon-5 text-muted me-2'
                />
                <span className='text-muted fs-7'>Credit Limit:</span>
                <span className='text-gray-800 fw-semibold ms-2'>
                  {overview.creditLimit || '-'}
                </span>
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <KTSVG
                  path='/media/icons/duotune/finance/fin010.svg'
                  className='svg-icon-5 text-muted me-2'
                />
                <span className='text-muted fs-7'>Credit Used:</span>
                <span className='text-gray-800 fw-semibold ms-2'>{overview.creditUsed || '-'}</span>
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <KTSVG
                  path='/media/icons/duotune/finance/fin006.svg'
                  className='svg-icon-5 text-muted me-2'
                />
                <span className='text-muted fs-7'>Outstanding Balance:</span>
                <span className='text-gray-800 fw-semibold ms-2'>
                  {overview.outstandingBalance || '-'}
                </span>
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <KTSVG
                  path='/media/icons/duotune/finance/fin006.svg'
                  className='svg-icon-5 text-muted me-2'
                />
                <span className='text-muted fs-7'>Store Credit:</span>
                <span className='text-gray-800 fw-semibold ms-2'>
                  {overview.storeCredit || '-'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default OverviewSection
