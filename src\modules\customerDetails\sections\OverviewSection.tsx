import React from 'react'
import {IOverview} from '../interfaces'
import Loading from '../../loading'
import {getBadgeColor} from '../../../utils/badge'
import usePermission from '../../../hook/usePermission'
import {KTSVG} from '../../../_metronic/helpers'
import {useState} from 'react'
import Date from '../../../components/Date/Date'
import CustomerTagsModal from './models/CustomerTagsModal'

interface OverviewSectionProps {
  overview: IOverview
  isLoading: boolean
}

const OverviewSection: React.FC<OverviewSectionProps> = ({overview, isLoading}) => {
  const {hasPermission} = usePermission()
  const [openModal, setOpenModal] = useState(false)

  return (
    <div className='row g-5 mb-5 position-relative'>
      {isLoading && <Loading />}

      {/* Top summary cards */}
      <div className='col-md-3'>
        <div className='card  border h-100'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center'>
              <div className='flex-grow-1'>
                <div className='text-muted fw-semibold fs-7 mb-1'>Total Orders</div>
                <div className='fw-bold fs-1'>{overview.totalOrders || 0}</div>
              </div>
              <div className='symbol symbol-50px bg-primary bg-opacity-10'>
                <div className='symbol-label'>
                  <i className='las la-shopping-cart fs-2 text-primary'></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className='col-md-3'>
        <div className='card border h-100'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center'>
              <div className='flex-grow-1'>
                <div className='text-muted fw-semibold fs-7 mb-1'>Total Spent</div>
                <div className='fw-bold fs-1'>{overview.totalRevenue || '-'}</div>
              </div>
              <div className='symbol symbol-50px bg-primary bg-opacity-10'>
                <div className='symbol-label'>
                  <i className='las la-dollar-sign fs-2 text-primary'></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className='col-md-3'>
        <div className='card  border h-100'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center'>
              <div className='flex-grow-1'>
                <div className='text-muted fw-semibold fs-7 mb-1'>Avg Order Value</div>
                <div className='fw-bold fs-1'>{overview.avgOrderValue || '-'}</div>
              </div>
              <div className='symbol symbol-50px bg-primary bg-opacity-10'>
                <div className='symbol-label'>
                  <i className='las la-chart-line fs-3 text-warning'></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className='col-md-3'>
        <div className='card  border h-100'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center'>
              <div className='flex-grow-1'>
                <div className='text-muted fw-semibold fs-7 mb-1'>Lifetime Value</div>
                <div className='fw-bold fs-1'>{overview.lifetimeValue || '-'}</div>
              </div>
              <div className='symbol symbol-50px bg-success bg-opacity-10'>
                <div className='symbol-label'>
                  <i className='las la-star fs-2 text-success'></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Details cards */}
      <div className='col-md-3'>
        <div className='card border h-100 mt-5'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-4'>
              <i className='las la-user-tag fs-2 text-primary me-2'></i>
              <h5 className='fw-bold m-0'>Information</h5>
            </div>
            {/* Tags */}
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <i className='las la-tags fs-5 text-muted me-2'></i>
                <span className='fw-bold'>Tags:</span>
                {hasPermission('customers_all customer', 'write') && (
                  <span onClick={() => setOpenModal(true)} className='cursor-pointer ms-2'>
                    <KTSVG path='/media/ad-theme-icons/edit.svg' />
                  </span>
                )}
                <span className='text-gray-700 ms-2 text-uppercase'>{overview?.tags || '-'}</span>
              </div>
            </div>
            {/* Customer Type */}
            <div className='mb-3'>
              <div className='d-flex align-top mb-2'>
                <i className='las la-layer-group fs-5 text-muted me-2'></i>
                <span className='fw-bold'> Type:</span>
                <span className='text-gray-700 ms-2'>{overview?.type || '-'}</span>
              </div>
            </div>
            {/* Customer Representative */}
            <div className='mb-3'>
              <div className='d-flex align-top mb-2'>
                <i className='las la-user-friends fs-5 text-muted me-2'></i>
                <span className='fw-bold'> Representative:</span>
                <span className='text-gray-700 ms-2'>{overview?.customerRepName || '-'}</span>
              </div>
            </div>
            {/* Customer Group */}
            <div className='mb-3'>
              <div className='d-flex align-top mb-2'>
                <i className='las la-users fs-5 text-muted me-2'></i>
                <span className='fw-bold'> Group:</span>
                <span className='text-gray-700 ms-2'>{overview?.customerGroupName || '-'}</span>
              </div>
            </div>
          </div>
        </div>
        {openModal && (
          <CustomerTagsModal
            customerId={overview?.customerId}
            openModal={openModal}
            onCancel={() => setOpenModal(false)}
            isEdit={!!overview?.tags?.length}
          />
        )}
      </div>
      <div className='col-md-3'>
        <div className='card border  h-100 mt-5'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-4'>
              <i className='las la-info-circle fs-2 text-primary me-2'></i>
              <h5 className='fw-bold m-0 '>Status & Dates</h5>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <i className='las la-check-circle fs-5 text-muted me-2 '></i>
                <span>Status:</span>
                {overview.status ? (
                  <span
                    className={`badge ${getBadgeColor(
                      overview.status,
                      'light'
                    )} badge-lg text-capitalize`}
                  >
                    {overview.status}
                  </span>
                ) : (
                  '-'
                )}
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <i className='las la-calendar fs-5 text-muted me-2'></i>
                <span>Created:</span>
                <span className='text-gray-700 ms-2'><Date date={overview.createdDate} /></span>
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <i className='las la-edit fs-5 text-muted me-2'></i>
                <span>Last Modified:</span>
                <span className='text-gray-700 ms-2'><Date date={overview.lastModifiedDate} /></span>
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <i className='las la-clock fs-5 text-muted me-2'></i>
                <span>Last Activity:</span>
                <span className='text-gray-700 ms-2'><Date date={overview.lastOrderDate} /></span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className='col-md-3'>
        <div className='card border  h-100 mt-5'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-4'>
              <i className='las la-users fs-2 text-primary me-2'></i>
              <h5 className='fw-bold m-0 '>Contact</h5>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <i className='las la-envelope fs-5 text-muted me-2'></i>
                <span>Email:</span>
                <span className='text-gray-700 ms-2'>{overview.email || '<EMAIL>'}</span>
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <i className='las la-phone fs-5 text-muted me-2'></i>
                <span>Phone:</span>
                <span className='text-gray-700 ms-2'>{overview.phone || '+****************'}</span>
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <i className='las la-globe fs-5 text-muted me-2'></i>
                <span>Website:</span>
                <span className='text-gray-700 ms-2'>{overview.website || 'https://acme.com'}</span>
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <i className='las la-user fs-5 text-muted me-2'></i>
                <span>Owner:</span>
                <span className='text-gray-700 ms-2'>{overview.owner || '-'}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className='col-md-3'>
        <div className='card border  h-100 mt-5'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-4'>
              <i className='las la-dollar-sign fs-2 text-primary me-2'></i>
              <h5 className='fw-bold m-0 '>Financial</h5>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <i className='las la-dollar-sign fs-5 text-muted me-2'></i>
                <span>Annual Revenue:</span>
                <span className='text-gray-700 ms-2'>{overview.annualRevenue}</span>
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <i className='las la-credit-card fs-5 text-muted me-2'></i>
                <span>Credit Limit:</span>
                <span className='text-gray-700 ms-2'>{overview.creditLimit || '$50,000'}</span>
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <i className='las la-coins fs-5 text-muted me-2'></i>
                <span>Credit Used:</span>
                <span className='text-gray-700 ms-2'>{overview.creditUsed}</span>
              </div>
            </div>
            <div className='mb-3'>
              <div className='d-flex align-items-center mb-2'>
                <i className='las la-exclamation-circle fs-5 text-muted me-2'></i>
                <span>Outstanding Balance:</span>
                <span className='text-gray-700 ms-2'>{overview.outstandingBalance}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default OverviewSection
