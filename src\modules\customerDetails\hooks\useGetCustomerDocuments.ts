import useFilters from '../../../hook/useFilters'
import useApi from '../../../services/useApi'
import { parseCustomerDocuments } from '../parsings/documents'
import { IDocument } from '../interfaces'

// Dummy data for development
const dummyDocumentsData = [
  {
    id: '1',
    document_name: 'Contract_2024.pdf',
    document_type: 'Contract',
    file_size: 2048576,
    uploaded_by: '<PERSON>',
    uploaded_date: '2024-01-15',
    last_modified_date: '2024-01-20',
    download_url: '/documents/contract_2024.pdf',
    is_active: true,
    category: 'Legal',
    description: 'Annual service contract for 2024',
  },
]

export default function useGetCustomerDocuments(customerId?: string) {
  const initialFilters = {
    page: 1,
    limit: 10,
  }
  const Api = useApi()
  const {filters, onSearch, onSortingChange, onPageChange} = useFilters(initialFilters)

  // For development, return dummy data instead of API call
  const useDummyData = true // Set to false when API is ready

  const {data: response, isFetching} = Api.useGetQuery(
    customerId && !useDummyData ? `/customers/${customerId}/documents` : '',
    {
      queryId: customerId ? `customer-documents-${customerId}` : undefined,
      filters,
      isErrorPageRedirection: false,
    },
    {
      enabled: !!customerId && !useDummyData,
    }
  )

  // Use dummy data or parsed API response
  const rawData = useDummyData ? dummyDocumentsData : response?.data
  const parsedDocuments: IDocument[] = parseCustomerDocuments(rawData)

  return {
    documents: parsedDocuments,
    isLoading: useDummyData ? false : isFetching,
    filters,
    onSearch,
    onSortingChange,
    onPageChange,
  }
}
