import useApi from '../../../services/useApi'
import {useParams} from 'react-router-dom'

export default function useGetCustomerDocuments() {
  const Api = useApi()
  const {id: customerId} = useParams<{id: string}>()
  const {data: response, isFetching} = Api.useGetQuery(
    `/customers/${customerId}/salesforce-documents`,
    {
      queryId: `customer-documents-${customerId}`,
      isErrorPageRedirection: false,
    },
    {
      enabled: !!customerId,
    }
  )

  return {
    documents: response?.data || [],
    isLoading: isFetching,
  }
}
