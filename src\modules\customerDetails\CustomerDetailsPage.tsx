import {PageLink} from '../../_metronic/layout/core'
import {Navigation} from '../../components/Navigation'
import useMeta from '../../hook/useMeta'
import AccountInfo from './layouts/AccountInfo'
import Addresses from './layouts/Addresses'
import Orders from './layouts/Orders'
import FinancialPayment from './layouts/FinancialPayment'
import Reward from './layouts/Reward'
import SystemActivity from './layouts/SystemActivity'
import Documents from './layouts/Documents'
import Overview from './layouts/Overview'
import CustomerSpecificPrice from './layouts/CustomerSpecificPrice'

const customerDetailsBreadcrumbs: PageLink[] = [
  {
    title: 'Customers',
    path: '/customers/all-customer/all',
    isActive: false,
  },
]

const CustomerDetailsPage = () => {
  useMeta('Customer Details')

  return (
    <Navigation
      baseUrl={'/customers/details/:id'}
      breadcrumbs={customerDetailsBreadcrumbs}
      navigationData={[
        {key: 'overview', label: 'Overview', component: <Overview />},
        {key: 'account-info', label: 'Account Info', component: <AccountInfo />},
        {key: 'addresses', label: 'Addresses', component: <Addresses />},
        {key: 'orders', label: 'Orders', component: <Orders />},
        {key: 'financial-payment', label: 'Financial & Payment', component: <FinancialPayment />},
        {key: 'reward', label: 'Reward', component: <Reward />},
        {key: 'pricing', label: 'Pricing', component: <CustomerSpecificPrice />},
        {key: 'system-activity', label: 'System & Activity', component: <SystemActivity />},
        {key: 'documents', label: 'Documents', component: <Documents />},
      ]}
    />
  )
}

export default CustomerDetailsPage
