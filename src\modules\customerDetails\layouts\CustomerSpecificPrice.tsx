import useMeta from '../../../hook/useMeta'
import {CustomerSpecificPriceContext} from '../context'
import {Pagination} from '../../../utils/pagination'
import {useGetCustomerSpecificPrice} from '../hooks/useGetCustomerSpecificPrice'
import { useState} from 'react'
import useCustomerSpecificPriceOperations from '../hooks/useCustomerSpecificPriceOperations'
import {useLocation} from 'react-router-dom'
import useGetProductsDropdown from '../hooks/useGetProductsDropdown'
import CustomerSpecificPriceHeader from '../sections/header/CustomerSpecificPriceHeader'
import CustomerSpecificPriceTable from '../sections/table/CustomerSpecificPriceTable'

const CustomerSpecificPrice = () => {
  useMeta('Customer Specific Price')
  const [checkedRows, setCheckedRows] = useState<any>([])

  const location = useLocation()
  const query = new URLSearchParams(location.search)
  const customerId = query.get('id') || (null as any)

  const {
    filters,
    onSortingChange,
    pagination,
    onPageChange,
    CustomerSpecificPriceData,
    isLoading,
    onSearch,
    CustomerSpecificPriceMeta,
    refetch,
    refetchFilter,
  } = useGetCustomerSpecificPrice(customerId)

  const {
    onAddCustomerSpecificPrice,
    onDeleteCustomerSpecificPrice,
    onUpdateCustomerSpecificPrice,
    isOperationLoading,
  } = useCustomerSpecificPriceOperations(customerId)

  const {
    products,
    pagination: productPagination,
    isLoading: isLoadingProducts,
    onPageChange: onProductPageChange,
    onSearch: onProductSearch,
  } = useGetProductsDropdown()

  const contextValue = {
    customerId,
    CustomerSpecificPriceData,
    isLoading,
    filters,
    onSortingChange,
    onSearch,
    CustomerSpecificPriceMeta,
    refetch,
    checkedRows,
    setCheckedRows,
    onAddCustomerSpecificPrice,
    onDeleteCustomerSpecificPrice,
    onUpdateCustomerSpecificPrice,
    isOperationLoading,
    refetchFilter,
    products,
    productPagination,
    isLoadingProducts,
    onProductPageChange,
    onProductSearch,
  }

  return (
    <CustomerSpecificPriceContext.Provider value={contextValue}>
      <CustomerSpecificPriceHeader />
      <CustomerSpecificPriceTable />
      <Pagination pagination={pagination} onPageChange={onPageChange} />
    </CustomerSpecificPriceContext.Provider>
  )
}

export default CustomerSpecificPrice
