import {useContext, useState} from 'react'
import {ConfirmationModal} from '../../../../components/ConfirmationModal'
import {useForm} from 'react-hook-form'
import {SectionForm} from '../../../../components/SectionForm'
import {yupResolver} from '@hookform/resolvers/yup'
import {AddPriceRuleSchema} from '../../validations'
import {CustomerSpecificPriceContext} from '../../context'

const AddPriceRuleModal = ({show, onClose}: any) => {
  const {
    onAddCustomerSpecificPrice,
    isOperationLoading,
    products,
    productPagination,
    isLoadingProducts,
    onProductPageChange,
    onProductSearch,
  } = useContext(CustomerSpecificPriceContext)

  const [errorMessage, setErrorMessage] = useState('')
  const {
    register,
    handleSubmit,
    control,
    formState: {errors, isValid},
    setError,
    reset,
  } = useForm<any>({
    mode: 'onChange',
    resolver: yupResolver(AddPriceRuleSchema),
    defaultValues: {
      parent_sku: '',
      email: '',
      price: '',
    },
  })

  const onCancel = () => {
    onClose()
    reset()
    setErrorMessage('')
  }

  const onCreate = async (data: any) => {
    if (isNaN(data.price) || parseFloat(data.price) <= 0) {
      setError('price', {type: 'manual', message: 'Please enter a valid price'})
      return
    }

    const payload = {
      parent_sku: data.parent_sku,
      email: '',
      price: parseFloat(data.price),
    }

    const res: any = await onAddCustomerSpecificPrice(payload, setError)
    if (res?.status === 200) {
      onCancel()
    } else {
      if (!res?.error?.key) {
        setErrorMessage(res?.error)
      } else {
        setErrorMessage('')
      }
    }
  }

  return (
    <ConfirmationModal
      show={show}
      title='Add Price Rule'
      onClose={onCancel}
      body={
        <div>
          <SectionForm
            id='create-price-rule'
            className='product-section '
            register={register}
            control={control}
            errors={errors}
            inputs={[
              {
                id: 'price-list-product',
                registerKey: 'parent_sku',
                isReachAsyncSelect: true,
                required: true,
                label: 'Products',
                placeholder: 'Select Products',
                options: products,
                labelKey: 'parent_product_name',
                valueKey: 'parent_product_sku',
                searchOptionsBy: ['label', 'value'],
                isLoading: isLoadingProducts,
                onSearch: onProductSearch,
                pagination: productPagination,
                isSingleSelect: true,
                optionTemplate: (props: any) => (
                  <label>
                    {props.label} - <span className='text-muted'>{props.value}</span>
                  </label>
                ),
                onPageChange: onProductPageChange,
                class: 'col-md-12 mb-7',
              },
              {
                id: 'price',
                registerKey: 'price',
                label: 'Price',
                isFloat: true,
                required: true,
                class: 'col-md-12 mb-7',
                maxLength: 10,
              },
            ]}
          />
          <span className='text-danger'>{errorMessage ?? errorMessage}</span>
        </div>
      }
      dialogClassName={'modal-ls'}
      onAction={handleSubmit(onCreate)}
      actionName='Add'
      actionBtnClass='btn-primary'
      isDisabled={!isValid}
      isOperationLoading={isOperationLoading}
    />
  )
}

export default AddPriceRuleModal
