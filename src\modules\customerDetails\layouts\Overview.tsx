import React from 'react'
import OverviewSection from '../sections/OverviewSection'
import useGetCustomerOverview from '../hooks/useGetCustomerOverview'
import {OverviewContext} from '../context'

const Overview = () => {
  const {overview, isLoading} = useGetCustomerOverview()
  const contextValue = {overview, isLoading}
  return (
    <OverviewContext.Provider value={contextValue}>
      <OverviewSection overview={overview} isLoading={isLoading} />
    </OverviewContext.Provider>
  )
}

export default Overview
