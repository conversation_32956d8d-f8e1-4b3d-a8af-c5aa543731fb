/* eslint-disable react-hooks/exhaustive-deps */
import React, {useEffect} from 'react'
import {useParams} from 'react-router-dom'
import OverviewSection from '../sections/OverviewSection'
import useGetCustomerOverview from '../hooks/useGetCustomerOverview'
import {OverviewContext} from '../context'
import useCreateAndUpdateTag from '../hooks/useCreateAndUpdateTag'

const Overview = ({setCustomerDetails}: any) => {
  const {overview, isLoading} = useGetCustomerOverview()
  const {isLoading: isOperationLoading, updateCustomerTags} = useCreateAndUpdateTag()
  const contextValue = {overview, isLoading, updateCustomerTags, isOperationLoading}
  const {id: customerId} = useParams<{id: string}>()
  const storageKey = `customerDetails_${customerId}`

  useEffect(() => {
    if (overview.status || overview.customerName) {
      const details = {
        status: overview.status,
        customerName: overview.customerName,
      }
      setCustomerDetails(details)
      localStorage.setItem(storageKey, JSON.stringify(details))
    }
  }, [overview])

  return (
    <OverviewContext.Provider value={contextValue}>
      <OverviewSection overview={overview} isLoading={isLoading} />
    </OverviewContext.Provider>
  )
}

export default Overview
