import React from 'react'
import DocumentsSection from '../sections/DocumentsSection'
import useGetCustomerDocuments from '../hooks/useGetCustomerDocuments'
import {DocumentsContext} from '../context'

const Documents = () => {
  const {documents, isLoading} = useGetCustomerDocuments()
  const contextValue = {documents, isLoading}
  return (
    <DocumentsContext.Provider value={contextValue}>
      <DocumentsSection />
    </DocumentsContext.Provider>
  )
}

export default Documents
