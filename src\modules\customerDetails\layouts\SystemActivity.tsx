import React from 'react'
import SystemActivitySection from '../sections/SystemActivitySection'
import useGetCustomerSystemActivity from '../hooks/useGetCustomerSystemActivity'
import {SystemActivityContext} from '../context'

const SystemActivity = () => {
  const {systemActivity, isLoading} = useGetCustomerSystemActivity()
  const contextValue = {systemActivity, isLoading}
  return (
    <SystemActivityContext.Provider value={contextValue}>
      <SystemActivitySection />
    </SystemActivityContext.Provider>
  )
}

export default SystemActivity
