import {useContext, useEffect, useState} from 'react'
import {ConfirmationModal} from '../../../../components/ConfirmationModal'
import Search from '../../../../components/Search'
import AddPriceRuleModal from '../models/AddPriceRuleModal'
import { CustomerSpecificPriceContext } from '../../context'
import { useAppSelector } from '../../../../redux/useTypedSelector'

export default function CustomerSpecificPriceHeader() {
  const {user} = useAppSelector((state) => state.auth)
  const {
    checkedRows,
    onDeleteCustomerSpecificPrice,
    setCheckedRows,
    isOperationLoading,
    isLoading,
    onSearch,
  } = useContext(CustomerSpecificPriceContext)
  const [showModal, setShowModal] = useState(false)
  const [reset, setReset] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)

  useEffect(() => {
    if (reset) {
      setReset(false)
    }
  }, [reset])

  const handleDeleteConfirm = () => {
    onDeleteCustomerSpecificPrice({ids: checkedRows})
    setCheckedRows([])
  }

  return (
    <div className='d-flex mb-10 justify-content-between'>
      <div className='d-flex gap-10'>
        <Search
          onSearch={(searchTerm: string) => {
            onSearch && onSearch(searchTerm)
          }}
          isClearInput={reset}
        />
        {checkedRows.length > 0 && (
          <button className='btn btn-danger' onClick={() => setShowDeleteModal(true)}>
            Delete
          </button>
        )}
      </div>

      {user?.is_owner && (
        <button className='btn btn-primary' onClick={() => setShowModal(true)}>
          + Add Price
        </button>
      )}

      <AddPriceRuleModal show={showModal} onClose={() => setShowModal(false)} />

      <ConfirmationModal
        show={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onAction={handleDeleteConfirm}
        disableAction={isOperationLoading}
        isOperationLoading={isOperationLoading}
        isDataLoading={isLoading}
      />
    </div>
  )
}
