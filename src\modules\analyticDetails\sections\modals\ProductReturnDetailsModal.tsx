import React, {useEffect} from 'react'
import {ConfirmationModal} from '../../../../components/ConfirmationModal'
import {DynamicTable} from '../../../../components/DynamicTable'
import Date from '../../../../components/Date/Date'
import useGetProductReturnDetails from '../../hooks/useGetProductReturnDetails'
import { Link } from 'react-router-dom'
import { formatPrice } from '../../../../utils/common'

export interface ProductReturnDetailsModalProps {
  show: boolean
  onClose: () => void
  variantSku?: string
  parentSku?: string
  dialogClassName?: string
  isParent?: boolean
  title: string
}

const ProductReturnDetailsModal: React.FC<ProductReturnDetailsModalProps> = ({
  show,
  onClose,
  parentSku = '',
  variantSku = '',
  dialogClassName = 'modal-xl',
  isParent = false,
  title,
}) => {
  const {
    productReturnDetails,
    isLoading,
    fetchProductReturnDetails,
    fetchProductVariantReturnDetails,
    onSortingChange
  } = useGetProductReturnDetails({
    variant_sku: variantSku,
    parent_sku: parentSku,
    isParent: isParent,
  })

  useEffect(() => {
    if (show) {
      if (isParent) {
        fetchProductReturnDetails(parentSku)
      } else {
        fetchProductVariantReturnDetails(variantSku)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [show, variantSku, parentSku, isParent])

  // Define table columns for product return details
  const productReturnTableColumns = [
    {
      key: 'order_id',
      label: 'Order ID',
      headerStyle: 'min-w-120px',
      isSorted: false,
    },
    {
      key: 'store_credit_amount',
      label: 'Store Credit',
      headerStyle: 'min-w-175px',
      isSorted: true,
    },
    {
      key: 'return_date',
      label: 'Date & Time',
      headerStyle: 'min-w-120px',
      isSorted: true,
    },
    ...(isParent
      ? [
          {
            key: 'variant_sku',
            label: 'SKU',
            headerStyle: 'min-w-120px',
            isSorted: true,
          },
        ]
      : []),
    {
      key: 'customer_name',
      label: 'Customer Name',
      headerStyle: 'min-w-250px',
      isSorted: true,
    },
    {
      key: 'order_quantity',
      label: 'Quantity',
      headerStyle: 'w-120px',
      isSorted: true,
    },
  ]

  // Prepare data for DynamicTable
  const tableData = Array.isArray(productReturnDetails)
    ? productReturnDetails
    : productReturnDetails
    ? [productReturnDetails]
    : []

  return (
    <ConfirmationModal
      show={show}
      onClose={onClose}
      dialogClassName={dialogClassName}
      bodyClass='mh-550px overflow-auto'
      disableFooter={false}
      disableAction={true}
      title={<div className='d-flex align-items-center'>{title}</div>}
      body={
        <DynamicTable
          data={tableData}
          sortableColumns={productReturnTableColumns}
          loading={isLoading}
          tableClass='table table-row-dashed table-row-gray-300 align-middle gs-0 gy-2'
          noDataMessage='No product return data available for this variant SKU.'
          onSortingChange={onSortingChange}
          TableRow={({row}: any) => {
            return (
              <tr>
                <td>
                  {row.order_id ? (
                    <Link
                      to={`/orders/all-orders/summary?id=${row.order_id}`}
                      className='text-dark text-hover-primary fs-6 text-decoration-underline'
                      target='_blank'
                    >
                      #{row.order_id}
                    </Link>
                  ) : (
                    "-"
                  )}
                </td>
                <td>
                  <span className='text-gray-800 fs-6'>
                    {formatPrice(row?.store_credit_amount, false)}
                  </span>
                </td>
                <td>
                  <Date date={row?.return_date} />
                </td>
                {isParent && (
                  <td>
                    <span className='text-gray-800 fs-6'>{row?.variant_sku || '-'}</span>
                  </td>
                )}
                <td>
                  <span className='text-gray-800 fs-6'>{row?.customer_name || '-'}</span>
                </td>
                <td>
                  <span className='text-gray-800 fs-6'>{row?.order_quantity || '0'}</span>
                </td>
              </tr>
            )
          }}
        />
      }
    />
  )
}

export default ProductReturnDetailsModal
