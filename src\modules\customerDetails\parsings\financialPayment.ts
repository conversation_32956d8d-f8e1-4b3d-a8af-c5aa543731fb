import { IFinancialPayment } from '../interfaces'

export const parseCustomerFinancialPayment = (data: any): IFinancialPayment => {
  if (!data) return {} as IFinancialPayment
  
  return {
    paymentCredit: {
      paymentTerm: data.payment_credit?.payment_term || data.paymentCredit?.paymentTerm || '',
      creditLimit: data.payment_credit?.credit_limit || data.paymentCredit?.creditLimit || '',
      unpaidBalance: data.payment_credit?.unpaid_balance || data.paymentCredit?.unpaidBalance || '',
      outstandingBalance: data.payment_credit?.outstanding_balance || data.paymentCredit?.outstandingBalance || '',
      paymentTermApprovedBy: data.payment_credit?.payment_term_approved_by || data.paymentCredit?.paymentTermApprovedBy || '',
      paymentTermApprovedDateTime: data.payment_credit?.payment_term_approved_date_time || data.paymentCredit?.paymentTermApprovedDateTime || '',
      paymentTermNotes: data.payment_credit?.payment_term_notes || data.paymentCredit?.paymentTermNotes || '',
      paymentTermIssues: data.payment_credit?.payment_term_issues || data.paymentCredit?.paymentTermIssues || '',
      paymentTermRisk: data.payment_credit?.payment_term_risk || data.paymentCredit?.paymentTermRisk || '',
      taxExempt: data.payment_credit?.tax_exempt || data.paymentCredit?.taxExempt || '',
      currency: data.payment_credit?.currency || data.paymentCredit?.currency || '',
    },
    cashCarry: {
      cashCarryCheckApproved: data.cash_carry?.cash_carry_check_approved || data.cashCarry?.cashCarryCheckApproved || '',
      checkApprovedBy: data.cash_carry?.check_approved_by || data.cashCarry?.checkApprovedBy || '',
      checkApprovedDateTime: data.cash_carry?.check_approved_date_time || data.cashCarry?.checkApprovedDateTime || '',
      checkApprovedAmountLimit: data.cash_carry?.check_approved_amount_limit || data.cashCarry?.checkApprovedAmountLimit || '',
      notedForCheckCnc: data.cash_carry?.noted_for_check_cnc || data.cashCarry?.notedForCheckCnc || '',
      doesCustomerHaveAccountWithCtos: data.cash_carry?.does_customer_have_account_with_ctos || data.cashCarry?.doesCustomerHaveAccountWithCtos || '',
    },
    creditCard: {
      customerCreditCardFeesExempt: data.credit_card?.customer_credit_card_fees_exempt || data.creditCard?.customerCreditCardFeesExempt || '',
      creditCardFeesExemptApprovedBy: data.credit_card?.credit_card_fees_exempt_approved_by || data.creditCard?.creditCardFeesExemptApprovedBy || '',
      creditCardFeesExemptApprovedDate: data.credit_card?.credit_card_fees_exempt_approved_date || data.creditCard?.creditCardFeesExemptApprovedDate || '',
    },
    ach: {
      achECheckApproved: data.ach?.ach_e_check_approved || data.ach?.achECheckApproved || '',
      achCheckApprovedBy: data.ach?.ach_check_approved_by || data.ach?.achCheckApprovedBy || '',
      achECheckedApprovedDateTime: data.ach?.ach_e_checked_approved_date_time || data.ach?.achECheckedApprovedDateTime || '',
      achFormReceivedUploaded: data.ach?.ach_form_received_uploaded || data.ach?.achFormReceivedUploaded || '',
      achFormUploadedBy: data.ach?.ach_form_uploaded_by || data.ach?.achFormUploadedBy || '',
      achFormReceivedUploadedDate: data.ach?.ach_form_received_uploaded_date || data.ach?.achFormReceivedUploadedDate || '',
    },
  }
}
