import { IFinancialPayment } from '../interfaces'

function boolToYesNo(val: any): string {
  if (val === true) return 'Yes'
  if (val === false) return 'No'
  return '-'
}

function safe(val: any): string {
  if (val === null || val === undefined || val === '') return '-'
  return String(val)
}

export const parseCustomerFinancialPayment = (data: any): IFinancialPayment => {
  if (!data) return {} as IFinancialPayment

  return {
    paymentAndCredit: {
      paymentTerm: safe(data.payment_and_credit?.payment_term),
      creditLimit: safe(data.payment_and_credit?.credit_limit),
      unpaidBalance: safe(data.payment_and_credit?.unpaid_balance),
      outstandingBalance: safe(data.payment_and_credit?.outstanding_balance),
      paymentTermApprovedBy: safe(data.payment_and_credit?.payment_term_approved_by),
      paymentTermApprovedDateTime: safe(data.payment_and_credit?.payment_term_approved_datetime),
      paymentTermNotes: safe(data.payment_and_credit?.payment_term_notes),
      paymentTermIssues: safe(data.payment_and_credit?.payment_term_issues),
      paymentTermRisk: safe(data.payment_and_credit?.payment_term_risk),
      taxExempt: boolToYesNo(data.payment_and_credit?.tax_exempt),
      currency: safe(data.payment_and_credit?.currency),
    },
    cashAndCarry: {
      cashAndCarryCheckApproved: boolToYesNo(data.cash_and_carry?.cash_and_carry_check_approved),
      checkApprovedBy: safe(data.cash_and_carry?.check_approved_by),
      checkApprovedDateTime: safe(data.cash_and_carry?.check_approved_datetime),
      checkApprovedAmountLimit: safe(data.cash_and_carry?.check_approved_amount_limit),
      notedForCheck: safe(data.cash_and_carry?.noted_for_check),
      doesTheCustomerHaveAccount: safe(data.cash_and_carry?.does_the_customer_have_account),
    },
    creditCard: {
      customerCreditCardFeesExempt: boolToYesNo(
        data.credit_card?.customer_credit_card_fees_exempt_no
      ),
      creditCardFeesExemptApprovedBy: safe(
        data.credit_card?.customer_credit_card_fees_exempt_approved_by
      ),
      creditCardFeesExemptApprovedDate: safe(
        data.credit_card?.customer_credit_card_fees_exempt_approved_date
      ),
    },
    ach: {
      achCheckApproved: boolToYesNo(data.ach?.ach_check_approved),
      achCheckApprovedBy: safe(data.ach?.ach_check_approved_by),
      achCheckApprovedDateTime: safe(data.ach?.ach_check_approved_datetime),
      achFormReceivedAndUploaded: boolToYesNo(data.ach?.ach_form_received_and_uploaded_no),
      achFormUploadedBy: safe(data.ach?.ach_form_uploaded_by),
      achFormReceivedUploadedDate: safe(data.ach?.ach_form_received_uploaded_date),
    },
    netTerms: {
      netTermReceivedAndUploaded: boolToYesNo(data.net_terms?.net_term_received_and_uploaded),
      netTermAgreementUploadedBy: safe(data.net_terms?.net_term_agreement_uploaded_by),
      netTermUploadedTime: safe(data.net_terms?.net_term_uploaded_time),
    },
  }
}
