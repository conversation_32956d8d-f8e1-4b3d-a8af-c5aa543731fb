import {useContext} from 'react'
import {Link} from 'react-router-dom'
import {SoldProductListingContext} from '../../context'
import useToastify from '../../../../hook/useToastify'
import usePermission from '../../../../hook/usePermission'

function SoldProductTableRow({row, handleCheckboxChange, handleSkuCheckboxChange, checkedIds}: any) {
  const {changeVisibility, lastOperationData}: any = useContext(SoldProductListingContext)
  const {toastMessage} = useToastify()
  const {hasPermission} = usePermission()

  const handleRowClick = () => {
    if (row['totalSold'] === 0) {
      toastMessage('error', "Product doesn't have sold Quantity")
    }
  }

  return (
    <tr>
      <td>
        <div className='form-check form-check-sm form-check-custom form-check-solid'>
          {hasPermission('analytics_sold products', 'write') ? (
            <input
              className='form-check-input widget-9-check'
              type='checkbox'
              checked={checkedIds.includes(row['id'])}
              onChange={() => {
                handleCheckboxChange(row['id'])
                handleSkuCheckboxChange(row['sku'])
              }}
            />
          ) : (
            <input className='form-check-input widget-9-check' type='checkbox' disabled={true} />
          )}
        </div>
      </td>
      <td>
        <div className='align-items-center'>
          {row['totalSold'] === 0 ? (
            <span className='text-dark text-hover-primary fs-6' onClick={handleRowClick}>
              {row['name']}
            </span>
          ) : (
            <Link
              to={`/analytics/sold-products/product-report/${row['id']}/${row['sku']}`}
              className='text-dark text-hover-primary fs-6'
              state={{productName: row['name']}}
              onClick={() => {
                localStorage.setItem('detailScreenProductName', row['name'])
              }}
            >
              {row['name']}
            </Link>
          )}
        </div>
      </td>
      <td>{row['sku']}</td>
      <td>{row['totalSold']}</td>
      <td>{row['availableQty']}</td>

      <td className='text-start'>
        {hasPermission('analytics_sold products', 'write') ? (
          <div className='d-flex justify-content-center flex-shrink-0'>
            {(row['id'] in lastOperationData && !lastOperationData[row['id']]) ||
            !row['isVisible'] ? (
              <i
                className='text-danger bi-eye-slash fs-2 cursor-pointer'
                onClick={() => changeVisibility([row['id']], '1')}
              />
            ) : (
              <i
                className='text-success bi bi-eye fs-2 cursor-pointer'
                onClick={() => changeVisibility([row['id']], '')}
              />
            )}
          </div>
        ) : (
          <div className='d-flex justify-content-center flex-shrink-0'>
            {(row['id'] in lastOperationData && !lastOperationData[row['id']]) ||
            !row['isVisible'] ? (
              <i className='text-danger bi-eye-slash fs-2' />
            ) : (
              <i className='text-success bi bi-eye fs-2' />
            )}
          </div>
        )}
      </td>
    </tr>
  )
}

export default SoldProductTableRow
