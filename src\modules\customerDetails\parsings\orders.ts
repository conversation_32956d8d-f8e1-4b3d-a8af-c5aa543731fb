import { IOrder, IOrderItem } from '../interfaces'

export const parseCustomerOrders = (data: any[]): IOrder[] => {
  if (!Array.isArray(data)) return []
  
  return data.map(order => ({
    id: order.id || '',
    orderNumber: order.order_number || order.orderNumber || '',
    orderDate: order.order_date || order.orderDate || '',
    status: order.status || '',
    totalAmount: order.total_amount || order.totalAmount || 0,
    currency: order.currency || 'USD',
    paymentMethod: order.payment_method || order.paymentMethod || '',
    shippingAddress: order.shipping_address || order.shippingAddress || '',
    billingAddress: order.billing_address || order.billingAddress || '',
    items: parseOrderItems(order.items || []),
    createdDate: order.created_date || order.createdDate || '',
    lastModifiedDate: order.last_modified_date || order.lastModifiedDate || '',
  }))
}

export const parseOrderItems = (items: any[]): IOrderItem[] => {
  if (!Array.isArray(items)) return []
  
  return items.map(item => ({
    id: item.id || '',
    productId: item.product_id || item.productId || '',
    productName: item.product_name || item.productName || '',
    sku: item.sku || '',
    quantity: item.quantity || 0,
    unitPrice: item.unit_price || item.unitPrice || 0,
    totalPrice: item.total_price || item.totalPrice || 0,
  }))
}
