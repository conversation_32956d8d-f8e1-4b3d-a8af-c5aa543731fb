import {IOrder} from '../interfaces'

export const parseCustomerOrders = (data: any[]): IOrder[] => {
  if (!Array.isArray(data)) return []

  return data.map((order) => ({
    id: order.id?.toString() || '',
    orderNumber: order.id?.toString() || '',
    customerName:
      [order.customer_first_name, order.customer_last_name].filter(Boolean).join(' ') || '',
    customerFirstNameLetter: order.customer_first_name?.charAt(0) || '',
    customerEmail: order.customer_email || '',
    orderDate: order.date_created || '',
    status: order.status || '',
    paymentStatus: order.payment_status || '',
    orderPaymentMethod: order.payment_method || '-',
    totalAmount: Number(order.total_including_tax) || 0,
    itemsCount: order.items_total || 0,
    orderChannel: order.channel || '-',
    shipTo: order.ship_to || '-',
  }))
}
