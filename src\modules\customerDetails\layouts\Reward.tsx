import React from 'react'
import RewardSection from '../sections/RewardSection'
import {useGetRewards} from '../hooks/useGetRewards'
import {RewardContext} from '../context'
import { useParams } from 'react-router-dom'

const Reward = () => {
  const {id: customerId} = useParams<{id: string}>()
  const {rewards, isLoading} = useGetRewards(customerId)
  const contextValue = {rewards, isLoading}
  return (
    <RewardContext.Provider value={contextValue}>
      <RewardSection />
    </RewardContext.Provider>
  )
}

export default Reward
