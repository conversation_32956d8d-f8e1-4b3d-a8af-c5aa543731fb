import { parseRewards } from '../utils';
import { getBadgeColor } from '../../../utils/badge';
import {formatPrice, formatToIndianNumber} from '../../../utils/common'
import Date from '../../../components/Date/Date'

const CustomerRewardsTableRow = ({row}: any) => {
  const customer = parseRewards(row)

  return (
    <tr>
      <td>
        <Date date={row['created_at']} />
      </td>
      <td>
        <div>{customer['order_id']}</div>
      </td>
      <td>
        {customer['order_status'] ? (
          <span className={`badge ${getBadgeColor(customer['order_status'], 'light')} badge-lg`}>
            <div>{customer['order_status']}</div>
          </span>
        ) : (
          '-'
        )}
      </td>
      <td>
        <div>{formatPrice(customer['order_total'], true)}</div>
      </td>
      <td>
        <div>{customer['coupon_code']}</div>
      </td>
      <td>
        <div>{formatToIndianNumber(customer['rewards'])}</div>
      </td>
      <td>
        <div>{formatToIndianNumber(customer['redemption'])}</div>
      </td>
      <td>
        <div>{formatToIndianNumber(customer['balance'])}</div>
      </td>
    </tr>
  )
}

export default CustomerRewardsTableRow;
