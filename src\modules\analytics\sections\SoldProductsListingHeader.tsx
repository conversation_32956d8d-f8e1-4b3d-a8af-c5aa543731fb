/* eslint-disable react-hooks/exhaustive-deps */
import {useContext} from 'react'
import {SoldProductListingContext} from '../context'
import Search from '../../../components/Search'
import {useNavigate} from 'react-router-dom'

function SoldProductsListingHeader({checkedIds, checkedSkuIds, handleClear}: any) {
  const {onSearch, changeVisibility} = useContext(SoldProductListingContext)
  const navigate = useNavigate()
  const handleVisibilityChange = async (visible: string) => {
    await changeVisibility(checkedIds, visible)
  }

  const handleCompareProducts = () => {
    navigate(`/analytics/sold-products/compare-products?sku=${checkedSkuIds.join(',')}`)
  }

  return (
    <div className='d-flex mb-10 justify-content-between'>
      <div className='d-flex gap-3'>
        <Search
          onSearch={(searchTerm: string) => {
            onSearch(searchTerm)
          }}
        />
        {checkedSkuIds?.length > 0 && (
          <div className='mt-8'>
            <div className='d-flex gap-3'>
              <button
                className='btn btn-primary'
                onClick={handleCompareProducts}
                disabled={checkedSkuIds?.length > 10}
              >
                Compare Products
              </button>
              <button className='btn btn-danger' onClick={handleClear}>
                Clear
              </button>
            </div>
            <div className='text-muted fs-7 fw-normal mt-1'>
              Maximum 10 products can be compared at a time
            </div>
          </div>
        )}
      </div>
      <div className=''>
        {checkedIds?.length ? (
          <button onClick={() => handleVisibilityChange('1')} className='btn btn-primary me-3'>
            Make It Visibility
          </button>
        ) : null}

        {checkedIds?.length ? (
          <button onClick={() => handleVisibilityChange('')} className='btn btn-danger'>
            Disable Visibility
          </button>
        ) : null}
      </div>
    </div>
  )
}

export default SoldProductsListingHeader
