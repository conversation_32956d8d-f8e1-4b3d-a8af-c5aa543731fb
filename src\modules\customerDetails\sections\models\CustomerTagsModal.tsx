import {Controller, useForm} from 'react-hook-form'
import {ConfirmationModal} from '../../../../components/ConfirmationModal'
import {TagPicker} from 'rsuite'
import {useContext, useEffect} from 'react'
import {OverviewContext} from '../../context'
import useGetCustomerTags from '../../hooks/useGetCustomerTags'
import useGetCustomerTagsDropdown from '../../../customers/hooks/useGetCustomerTagsDropdown'

const CustomerTagsModal = ({customerId, openModal, onCancel, isEdit}: any) => {
  const {overview, updateCustomerTags, isLoading, isOperationLoading} = useContext(OverviewContext)
  const {customerTags, isLoading: isLoadingTags} = useGetCustomerTagsDropdown()

  // Use the new hook for fetching assigned tags
  const {
    tags: assignedTags,
    isLoading: loadingTags,
    isFetching: fetchingCustomerTags,
  } = useGetCustomerTags(customerId, isEdit)

  const {
    formState: {errors, dirtyFields},
    control,
    reset,
    handleSubmit,
  } = useForm<any>({
    defaultValues: {
      tags: isEdit ? assignedTags?.map((tag: any) => tag?.tag_label) : [],
    },
    shouldFocusError: false,
  })

  useEffect(() => {
    if (isEdit && !fetchingCustomerTags && assignedTags?.length > 0) {
      reset({
        tags: assignedTags?.map((tag: any) => tag?.tag_label),
      })
    }
  }, [assignedTags, fetchingCustomerTags, reset, isEdit])

  const onSubmit = (data: any) => {
    const payload = {
      tags: data.tags?.join(', '),
    }
    updateCustomerTags(payload, overview?.customerId)
  }

  return (
    <ConfirmationModal
      show={openModal}
      onClose={onCancel}
      onAction={handleSubmit(onSubmit)}
      title={isEdit ? 'Update Tags' : 'Add Tags'}
      actionName={isEdit ? 'Update' : 'Add'}
      actionBtnClass='btn-primary'
      isOperationLoading={isOperationLoading}
      isDataLoading={isLoading}
      isDisabled={isLoading || !dirtyFields?.tags}
      body={
        <form>
          <div className='position-relative'>
            <Controller
              name='tags'
              control={control}
              render={({field}) => (
                <>
                  <label className='mb-2 required'>Customer Tag:</label>
                  <TagPicker
                    {...field}
                    id='product-tags'
                    data={customerTags}
                    value={field.value}
                    loading={isLoadingTags || loadingTags || fetchingCustomerTags}
                    disabled={isLoadingTags || loadingTags || fetchingCustomerTags}
                    labelKey='tag_label'
                    valueKey='tag_label'
                    creatable
                    className={`w-100 text-uppercase ${
                      errors?.tags ? 'is-invalid border border-danger' : ''
                    }`}
                    menuStyle={{zIndex: 1100}}
                    menuClassName='text-uppercase'
                    onSelect={(value) => {
                      const validValues = value.filter(
                        (v: any) => v && v.trim() && !v.startsWith(' ')
                      )
                      field.onChange(validValues)
                    }}
                    onKeyDown={(e: any) => {
                      const value = e.target.value
                      const cursorPosition = e.target.selectionStart
                      if (/^\s|(\s{2,})/.test(value.slice(0, cursorPosition) + e.key)) {
                        e.preventDefault()
                      }
                    }}
                    onBlur={(e: any) => {
                      e.target.value = e.target.value.trim()
                    }}
                    onCreate={(value) => {
                      const validValues = value.filter(
                        (v: any) => v && v.trim() && !v.startsWith(' ')
                      )
                      field.onChange(validValues)
                    }}
                    onClean={() => {
                      field.onChange([])
                    }}
                  />
                  {errors?.tags && (
                    <div className='invalid-feedback'>{errors.tags.message as string}</div>
                  )}
                </>
              )}
            />
          </div>
        </form>
      }
    />
  )
}

export default CustomerTagsModal
