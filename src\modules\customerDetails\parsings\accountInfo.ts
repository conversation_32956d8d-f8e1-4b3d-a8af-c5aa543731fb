import { IAccountInfo } from '../interfaces'

export const parseCustomerAccountInfo = (data: any): IAccountInfo => {
  if (!data) return {} as IAccountInfo
  
  return {
    generalInformation: {
      accountName: data.general_info?.account_name || '-',
      accountNumber: data.general_info?.account_number || '-',
      accountType: data.general_info?.type || '-',
      status: data.general_info?.status || '-',
      tier: data.general_info?.tier || '-',
      industry: data.general_info?.industry || '-',
      website: data.general_info?.website || '-',
      customerPriority: data.general_info?.customer_priority || '-',
      rating: data.general_info?.rating || '-',
      federalTaxId: data.general_info?.federal_tax_identification || '-',
      howDidYouHearAboutUs: data.general_info?.how_did_you_hear_about_us || '-',
    },
    ownerManager: {
      accountOwner: data.owner_and_manager?.account_owner || '-',
      accountOwnerEmail: data.owner_and_manager?.account_owner_email || '-',
      accountManager: data.owner_and_manager?.account_manager || '-',
      accountManagerEmail: data.owner_and_manager?.account_manager_email || '-',
      accountManagerMobile: data.owner_and_manager?.account_manager_mobile || '-',
      accountManagerDivision: data.owner_and_manager?.account_manager_division || '-',
      accountManagerAssignedDateTime: data.owner_and_manager?.account_manager_assigned_date_and_time || '-',
    },
    bcInformation: {
      bcCustomerGroup: data.bc_info?.bc_customer_group || '-',
      bcGroupName: data.bc_info?.bc_customer_group_name || '-',
      bcEmail: data.bc_info?.bc_email || '-',
      bcStoreCredit: data.bc_info?.bc_store_credit?.toString() || '-',
      bcCustomerId:
        data.bc_info?.bc_customer_id_txt || data.bc_info?.bc_customer_id?.toString() || '-',
      bcModifiedTime: data.bc_info?.bc_modified_time || '-',
      cellMobileNumber: data.bc_info?.mobile_number || '-',
      smsConsent: data.bc_info?.sms_consent || '-',
    },
    formFields: {
      readAgreeMidwestTerms: data.form_fields?.read_agree_midwest_terms ? 'Yes' : 'No',
      areYouABusiness: data.form_fields?.are_you_a_business ? 'Yes' : 'No',
    },
    customOtherFields: {
      abuser: data.custom_and_other_fields?.abuser || '-',
      isPartner: data.custom_and_other_fields?.is_partner || '-',
      isVendor: data.custom_and_other_fields?.is_vendor || '-',
      isCustomerPortal: data.custom_and_other_fields?.is_customer_portal || '-',
      isProspect: data.custom_and_other_fields?.is_prospect || '-',
      isDeleted: data.custom_and_other_fields?.is_deleted || '-',
    },
  }
}
