import { IAccountInfo } from '../interfaces'

export const parseCustomerAccountInfo = (data: any): IAccountInfo => {
  if (!data) return {} as IAccountInfo
  
  return {
    generalInformation: {
      accountName: data.general_info?.account_name ||  'Not Set',
      accountNumber: data.general_info?.account_number ||  'Not Set',
      accountType: data.general_info?.type ||  'Not Set',
      status: data.general_info?.status ||  'Not Set',
      tier: data.general_info?.tier ||  'Not Set',
      industry: data.general_info?.industry ||  'Not Set',
      website: data.general_info?.website ||  'Not Set',
      customerPriority: data.general_info?.customer_priority ||  'Not Set',
      rating: data.general_info?.rating ||  'Not Set',
      federalTaxId: data.general_info?.federal_tax_identification ||  'Not Set',
      howDidYouHearAboutUs: data.general_info?.how_did_you_hear_about_us ||  'Not Set',
    },
    ownerManager: {
      accountOwner: data.owner_and_manager?.account_owner ||  'Not Set',
      accountOwnerEmail: data.owner_and_manager?.account_owner_email ||  'Not Set',
      accountManager: data.owner_and_manager?.account_manager ||  'Not Set',
      accountManagerEmail: data.owner_and_manager?.account_manager_email ||  'Not Set',
      accountManagerMobile: data.owner_and_manager?.account_manager_mobile ||  'Not Set',
      accountManagerDivision: data.owner_and_manager?.account_manager_division ||  'Not Set',
      accountManagerAssignedDateTime: data.owner_and_manager?.account_manager_assigned_date_and_time ||  'Not Set',
    },
    bcInformation: {
      bcCustomerGroup: data.bc_info?.bc_customer_group ||  'Not Set',
      bcGroupName: data.bc_info?.bc_customer_group_name ||  'Not Set',
      bcEmail: data.bc_info?.bc_email ||  'Not Set',
      bcStoreCredit: data.bc_info?.bc_store_credit?.toString() ||  'Not Set',
      bcCustomerId:
        data.bc_info?.bc_customer_id_txt || data.bc_info?.bc_customer_id?.toString() ||  'Not Set',
      bcModifiedTime: data.bc_info?.bc_modified_time ||  'Not Set',
      cellMobileNumber: data.bc_info?.mobile_number ||  'Not Set',
      smsConsent: data.bc_info?.sms_consent ||  'Not Set',
    },
    formFields: {
      readAgreeMidwestTerms: data.form_fields?.read_agree_midwest_terms ? 'Yes' : 'No',
      areYouABusiness: data.form_fields?.are_you_a_business ? 'Yes' : 'No',
    },
    customOtherFields: {
      abuser: data.custom_and_other_fields?.abuser ||  'Not Set',
      isPartner: data.custom_and_other_fields?.is_partner ||  'Not Set',
      isVendor: data.custom_and_other_fields?.is_vendor ||  'Not Set',
      isCustomerPortal: data.custom_and_other_fields?.is_customer_portal ||  'Not Set',
      isProspect: data.custom_and_other_fields?.is_prospect ||  'Not Set',
      isDeleted: data.custom_and_other_fields?.is_deleted ||  'Not Set',
    },
  }
}
