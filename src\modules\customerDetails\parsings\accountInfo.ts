import { IAccountInfo } from '../interfaces'

export const parseCustomerAccountInfo = (data: any): IAccountInfo => {
  if (!data) return {} as IAccountInfo
  
  return {
    generalInformation: {
      accountName: data.general_information?.account_name || data.generalInformation?.accountName || '',
      accountNumber: data.general_information?.account_number || data.generalInformation?.accountNumber || '',
      accountType: data.general_information?.account_type || data.generalInformation?.accountType || '',
      status: data.general_information?.status || data.generalInformation?.status || '',
      tier: data.general_information?.tier || data.generalInformation?.tier || '',
      industry: data.general_information?.industry || data.generalInformation?.industry || '',
      website: data.general_information?.website || data.generalInformation?.website || '',
      customerPriority: data.general_information?.customer_priority || data.generalInformation?.customerPriority || '',
      rating: data.general_information?.rating || data.generalInformation?.rating || '',
      federalTaxId: data.general_information?.federal_tax_id || data.generalInformation?.federalTaxId || '',
      howDidYouHearAboutUs: data.general_information?.how_did_you_hear_about_us || data.generalInformation?.howDidYouHearAboutUs || '',
    },
    ownerManager: {
      accountOwner: data.owner_manager?.account_owner || data.ownerManager?.accountOwner || '',
      accountOwnerEmail: data.owner_manager?.account_owner_email || data.ownerManager?.accountOwnerEmail || '',
      accountManager: data.owner_manager?.account_manager || data.ownerManager?.accountManager || '',
      accountManagerEmail: data.owner_manager?.account_manager_email || data.ownerManager?.accountManagerEmail || '',
      accountManagerMobile: data.owner_manager?.account_manager_mobile || data.ownerManager?.accountManagerMobile || '',
      accountManagerDivision: data.owner_manager?.account_manager_division || data.ownerManager?.accountManagerDivision || '',
      accountManagerAssignedDateTime: data.owner_manager?.account_manager_assigned_date_time || data.ownerManager?.accountManagerAssignedDateTime || '',
    },
    bcInformation: {
      bcCustomerGroup: data.bc_information?.bc_customer_group || data.bcInformation?.bcCustomerGroup || '',
      bcGroupName: data.bc_information?.bc_group_name || data.bcInformation?.bcGroupName || '',
      bcEmail: data.bc_information?.bc_email || data.bcInformation?.bcEmail || '',
      bcStoreCredit: data.bc_information?.bc_store_credit || data.bcInformation?.bcStoreCredit || '',
      bcCustomerId: data.bc_information?.bc_customer_id || data.bcInformation?.bcCustomerId || '',
      bcModifiedTime: data.bc_information?.bc_modified_time || data.bcInformation?.bcModifiedTime || '',
      cellMobileNumber: data.bc_information?.cell_mobile_number || data.bcInformation?.cellMobileNumber || '',
      smsConsent: data.bc_information?.sms_consent || data.bcInformation?.smsConsent || '',
    },
    formFields: {
      readAgreeMidwestTerms: data.form_fields?.read_agree_midwest_terms || data.formFields?.readAgreeMidwestTerms || '',
      areYouABusiness: data.form_fields?.are_you_a_business || data.formFields?.areYouABusiness || '',
    },
    customOtherFields: {
      abuser: data.custom_other_fields?.abuser || data.customOtherFields?.abuser || '',
      isPartner: data.custom_other_fields?.is_partner || data.customOtherFields?.isPartner || '',
      isVendor: data.custom_other_fields?.is_vendor || data.customOtherFields?.isVendor || '',
      isCustomerPortal: data.custom_other_fields?.is_customer_portal || data.customOtherFields?.isCustomerPortal || '',
      isProspect: data.custom_other_fields?.is_prospect || data.customOtherFields?.isProspect || '',
      isDeleted: data.custom_other_fields?.is_deleted || data.customOtherFields?.isDeleted || '',
    },
  }
}
