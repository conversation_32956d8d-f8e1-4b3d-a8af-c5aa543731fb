import {useContext} from 'react'
import {AnalyticsCompareProductsContext} from '../../context'
import PredefinedDateRange from '../../../../components/DateRangePicker/PreDefinedDateRange'

const AnalyticsCompareProductsHeader = () => {
  const {products, onDateChange} = useContext(AnalyticsCompareProductsContext)

  const handleDateChange = (startDate: any, endDate: any) => {
    onDateChange(startDate, endDate)
  }

  return (
    <div className='mb-8'>
      {/* Page Title */}
      <div className='d-flex align-items-center justify-content-between mb-6'>
        <h1 className='page-heading d-flex text-dark fw-bold fs-1 flex-column justify-content-center my-0'>
          Product Comparison
          <span className='page-desc text-muted fs-6 fw-normal'>
            Compare {products?.length} products side by side
          </span>
        </h1>
        <PredefinedDateRange
          cb={handleDateChange}
          defaultValue={'This Month'}
          getLbl={() => {}}
          includeCurrantDay={true}
          isStoreDateInLocalStorage={false}
          minPastDaysAllowed={455}
        />
      </div>
    </div>
  )
}

export default AnalyticsCompareProductsHeader
