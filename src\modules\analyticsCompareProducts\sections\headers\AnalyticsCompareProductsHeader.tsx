import {useContext} from 'react'
import {AnalyticsCompareProductsContext} from '../../context'
import Search from '../../../../components/Search'

const AnalyticsCompareProductsHeader = () => {
  const {onSearch, filters} = useContext(AnalyticsCompareProductsContext)

  return (
    <div className='d-flex justify-content-between align-items-center mb-10'>
      <div className='d-flex align-items-center gap-5'>
        <Search
          onSearch={(value: string) => {
            onSearch(value)
          }}
          placeholder='Search by SKU or Name'
        />
      </div>
      {/* Placeholder for future filters */}
      <div className='d-flex gap-5'>{/* Add filter components here if needed */}</div>
    </div>
  )
}

export default AnalyticsCompareProductsHeader
