import {useContext} from 'react'
import {AnalyticsCompareProductsContext} from '../../context'

const AnalyticsCompareProductsHeader = () => {
  const {products} = useContext(AnalyticsCompareProductsContext)

  return (
    <div className='mb-8'>
      {/* Page Title */}
      <div className='d-flex align-items-center mb-6'>
        <h1 className='page-heading d-flex text-dark fw-bold fs-1 flex-column justify-content-center my-0'>
          Product Comparison
          <span className='page-desc text-muted fs-6 fw-normal'>
            Compare {products.length} products side by side
          </span>
        </h1>
      </div>
    </div>
  )
}

export default AnalyticsCompareProductsHeader
