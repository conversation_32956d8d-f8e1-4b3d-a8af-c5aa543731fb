import {useContext} from 'react'
import {AnalyticsCompareProductsContext} from '../../context'
import PredefinedDateRange from '../../../../components/DateRangePicker/PreDefinedDateRange'

const AnalyticsCompareProductsHeader = () => {
  const {onDateChange} = useContext(AnalyticsCompareProductsContext)

  const handleDateChange = (startDate: any, endDate: any) => {
    onDateChange(startDate, endDate)
  }

  return (
    <div className='mb-8'>
      {/* Page Title */}
      <div className='d-flex align-items-center justify-content-end mb-6'>
        <PredefinedDateRange
          cb={handleDateChange}
          defaultValue={'This Month'}
          getLbl={() => {}}
          includeCurrantDay={true}
          isStoreDateInLocalStorage={false}
          minPastDaysAllowed={455}
        />
      </div>
    </div>
  )
}

export default AnalyticsCompareProductsHeader
