export const getFileIconClass = (ext: any) => {
  switch ((ext || '').toLowerCase()) {
    case 'pdf':
      return 'bi bi-file-earmark-pdf text-danger'
    case 'doc':
    case 'docx':
      return 'bi bi-file-earmark-word text-primary'
    case 'xls':
    case 'xlsx':
      return 'bi bi-file-earmark-excel text-success'
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return 'bi bi-file-earmark-image text-info'
    default:
      return 'bi bi-file-earmark text-muted'
  }
}

export const toUnderscoreName = (name: string) => name.replace(/\s+/g, '_')
