export const getFileIconClass = (ext: any) => {
  switch ((ext || '').toLowerCase()) {
    case 'pdf':
      return 'bi bi-file-earmark-pdf text-danger'
    case 'doc':
    case 'docx':
      return 'bi bi-file-earmark-word text-primary'
    case 'xls':
    case 'xlsx':
      return 'bi bi-file-earmark-excel text-success'
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return 'bi bi-file-earmark-image text-info'
    default:
      return 'bi bi-file-earmark text-muted'
  }
}

export const toUnderscoreName = (name: string) => name.replace(/\s+/g, '_')

// =============== Product Return Page Utils ===============

// Product Return Table Columns
export const productReturnTableColumns = [
  {
    key: 'expand',
    label: '',
    headerStyle: 'min-w-50px w-50px mw-50px',
  },
  {
    key: 'parent_product_title',
    label: 'Product Name',
    headerStyle: 'min-w-700px w-700px',
    isSorted: true,
  },
  {
    key: 'parent_sku',
    label: 'SKU',
    headerStyle: 'min-w-150px w-150px',
    isSorted: true,
  },
  {
    key: 'total_product_returned_quantity',
    label: 'Product Return',
    headerStyle: 'min-w-180px w-150px',
    isSorted: true,
  },
]

// Product Variants Return Table Columns
export const productVariantsReturnTableColumns = [
  {
    key: 'expand',
    label: '',
    headerStyle: 'min-w-50px w-50px mw-50px',
  },
  {
    key: 'parent_product_title',
    label: 'Product Name',
    headerStyle: 'min-w-700px w-700px',
    isSorted: false,
  },
  {
    key: 'parent_sku',
    label: 'SKU',
    headerStyle: 'min-w-150px w-150px',
    isSorted: false,
  },
  {
    key: 'total_product_returned_quantity',
    label: 'Product Return',
    headerStyle: 'min-w-180px w-150px',
    isSorted: false,
  },
]

// Product Return Details Modal Table Columns
export const productReturnDetailsModalTableColumns = (isParent: boolean) => [
  {
    key: 'order_id',
    label: 'Order ID',
    headerStyle: 'min-w-120px',
    isSorted: false,
  },
  {
    key: 'store_credit_amount',
    label: 'Store Credit',
    headerStyle: 'min-w-175px',
    isSorted: false,
  },
  {
    key: 'return_date',
    label: 'Date & Time',
    headerStyle: 'min-w-120px',
    isSorted: false,
  },
  ...(isParent
    ? [
        {
          key: 'variant_sku',
          label: 'SKU',
          headerStyle: 'min-w-120px',
          isSorted: false,
        },
      ]
    : []),
  {
    key: 'customer_name',
    label: 'Customer Name',
    headerStyle: 'min-w-250px',
    isSorted: false,
  },
  {
    key: 'quantity',
    label: 'Quantity',
    headerStyle: 'w-120px',
    isSorted: false,
  },
]

export const getExpandIcon = (isExpanded: boolean, hasChildren: boolean): string => {
  if (!hasChildren) return ''
  return isExpanded
    ? 'fas bi bi-dash-circle fs-3 fw-semibold cursor-pointer'
    : 'fas bi bi-plus-circle fs-3 fw-semibold cursor-pointer'
}

// Helper Functions
export const formatReturnCount = (count: number): string => {
  return count.toLocaleString('en-IN')
}
