import useApi from '../../../services/useApi'
import {parseCustomerFinancialPayment} from '../parsings/financialPayment'
import {useParams} from 'react-router-dom'

export default function useGetCustomerFinancialPayment() {
  const Api = useApi()
  const {id: customerId} = useParams<{id: string}>()

  const {data: response, isFetching} = Api.useGetQuery(
    `/customers/${customerId}/salesforce-financial-payment`,
    {
      queryId: customerId ? `customer-financial-payment-${customerId}` : undefined,
      isErrorPageRedirection: false,
    },
    {
      enabled: !!customerId,
    }
  )

  return {
    financialPayment: parseCustomerFinancialPayment(response?.data),
    isLoading: isFetching,
  }
}
