import useFilters from '../../../hook/useFilters'
import useApi from '../../../services/useApi'
import { parseCustomerFinancialPayment } from '../parsings/financialPayment'
import { IFinancialPayment } from '../interfaces'

// Dummy data for development
const dummyFinancialPaymentData = {
  payment_credit: {
    payment_term: 'Not Set',
    credit_limit: '$50,000',
    unpaid_balance: 'Not Set',
    outstanding_balance: 'Not Set',
    payment_term_approved_by: 'Not Set',
    payment_term_approved_date_time: 'Not Set',
    payment_term_notes: 'Not Set',
    payment_term_issues: 'Not Set',
    payment_term_risk: 'Not Set',
    tax_exempt: 'No',
    currency: 'Not Set',
  },
  cash_carry: {
    cash_carry_check_approved: 'No',
    check_approved_by: 'Not Set',
    check_approved_date_time: 'Not Set',
    check_approved_amount_limit: 'Not Set',
    noted_for_check_cnc: 'Not Set',
    does_customer_have_account_with_ctos: 'Not Set',
  },
  credit_card: {
    customer_credit_card_fees_exempt: 'No',
    credit_card_fees_exempt_approved_by: 'Not Set',
    credit_card_fees_exempt_approved_date: 'Not Set',
  },
  ach: {
    ach_e_check_approved: 'No',
    ach_check_approved_by: 'Not Set',
    ach_e_checked_approved_date_time: 'Not Set',
    ach_form_received_uploaded: 'No',
    ach_form_uploaded_by: 'Not Set',
    ach_form_received_uploaded_date: 'Not Set',
  },
}

export default function useGetCustomerFinancialPayment(customerId?: string) {
  const initialFilters = {
    page: 1,
    limit: 1,
  }
  const Api = useApi()
  const {filters, onSearch, onSortingChange, onPageChange} = useFilters(initialFilters)

  // For development, return dummy data instead of API call
  const useDummyData = true // Set to false when API is ready

  const {data: response, isFetching} = Api.useGetQuery(
    customerId && !useDummyData ? `/customers/${customerId}/financial-payment` : '',
    {
      queryId: customerId ? `customer-financial-payment-${customerId}` : undefined,
      filters,
      isErrorPageRedirection: false,
    },
    {
      enabled: !!customerId && !useDummyData,
    }
  )

  // Use dummy data or parsed API response
  const rawData = useDummyData ? dummyFinancialPaymentData : response?.data
  const parsedFinancialPayment: IFinancialPayment = parseCustomerFinancialPayment(rawData)

  return {
    financialPayment: parsedFinancialPayment,
    isLoading: useDummyData ? false : isFetching,
    filters,
    onSearch,
    onSortingChange,
    onPageChange,
  }
}
