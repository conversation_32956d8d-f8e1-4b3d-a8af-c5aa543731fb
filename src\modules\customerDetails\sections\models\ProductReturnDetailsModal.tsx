/* eslint-disable react-hooks/exhaustive-deps */
import {useEffect} from 'react'
import {ConfirmationModal} from '../../../../components/ConfirmationModal'
import {DynamicTable} from '../../../../components/DynamicTable'
import Date from '../../../../components/Date/Date'
import useGetProductReturnDetails from '../../hooks/useGetProductReturnDetails'
import {productReturnDetailsModalTableColumns} from '../../utils'
import {ProductReturnDetailsModalProps} from '../../interfaces'
import { Link } from 'react-router-dom'
import { formatPrice } from '../../../../utils/common'

const ProductReturnDetailsModal = ({
  show,
  onClose,
  parentSku = '',
  variantSku = '',
  dialogClassName = 'modal-xl',
  isParent = false,
  title,
}: ProductReturnDetailsModalProps) => {
  const {productReturnDetails, isLoading, fetchProductReturnDetails} = useGetProductReturnDetails({
    variant_sku: variantSku,
    parent_sku: parentSku,
  })

  // Effect: Fetch product / variant return details
  useEffect(() => {
    if (show && (variantSku || parentSku)) {
      fetchProductReturnDetails(parentSku, variantSku)
    }
  }, [show, variantSku, parentSku])

  return (
    <ConfirmationModal
      show={show}
      onClose={onClose}
      dialogClassName={dialogClassName}
      bodyClass='mh-550px overflow-auto'
      disableFooter={false}
      disableAction={true}
      title={<div className='d-flex align-items-center'>{title}</div>}
      body={
        <DynamicTable
          data={productReturnDetails}
          sortableColumns={productReturnDetailsModalTableColumns(isParent)}
          loading={isLoading}
          tableClass='table table-row-dashed table-row-gray-300 align-middle gs-0 gy-2'
          noDataMessage='No product return data available for this variant SKU.'
          TableRow={({row}: any) => {
            return (
              <tr>
                <td>
                  {row.order_id ? (
                    <Link
                      to={`/orders/all-orders/summary?id=${row.order_id}`}
                      className='text-dark text-hover-primary fs-6 text-decoration-underline'
                      target='_blank'
                    >
                      #{row.order_id}
                    </Link>
                  ) : (
                    "-"
                  )}
                </td>
                <td>
                  <span className='text-gray-800 fs-6'>
                    {formatPrice(row?.store_credit_amount, false)}
                  </span>
                </td>
                <td>
                  <Date date={row?.return_date} />
                </td>
                {isParent && (
                  <td>
                    <span className='text-gray-800 fs-6'>{row?.variant_sku || '-'}</span>
                  </td>
                )}
                <td>
                  <span className='text-gray-800 fs-6'>{row?.customer_name || '-'}</span>
                </td>
                <td>
                  <span className='text-gray-800 fs-6'>{row?.order_quantity || '0'}</span>
                </td>
              </tr>
            )
          }}
        />
      }
    />
  )
}

export default ProductReturnDetailsModal
