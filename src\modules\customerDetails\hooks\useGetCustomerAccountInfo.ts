import useFilters from '../../../hook/useFilters'
import useApi from '../../../services/useApi'
import { parseCustomerAccountInfo } from '../parsings/accountInfo'
import { IAccountInfo } from '../interfaces'

// Dummy data for development
const dummyAccountInfoData = {
  general_information: {
    account_name: 'Acme Corporation',
    account_number: 'Not Set',
    account_type: 'Business',
    status: 'Active',
    tier: 'Gold',
    industry: 'Technology',
    website: 'https://acme.com',
    customer_priority: 'Not Set',
    rating: 'Not Set',
    federal_tax_id: 'Not Set',
    how_did_you_hear_about_us: 'Not Set',
  },
  owner_manager: {
    account_owner: 'Not Set',
    account_owner_email: 'Not Set',
    account_manager: '<PERSON>',
    account_manager_email: 'Not Set',
    account_manager_mobile: 'Not Set',
    account_manager_division: 'Not Set',
    account_manager_assigned_date_time: 'Not Set',
  },
  bc_information: {
    bc_customer_group: 'Not Set',
    bc_group_name: 'Not Set',
    bc_email: 'Not Set',
    bc_store_credit: 'Not Set',
    bc_customer_id: 'Not Set',
    bc_modified_time: 'Not Set',
    cell_mobile_number: 'Not Set',
    sms_consent: 'Not Set',
  },
  form_fields: {
    read_agree_midwest_terms: 'Not Set',
    are_you_a_business: 'Not Set',
  },
  custom_other_fields: {
    abuser: 'Not Set',
    is_partner: 'Not Set',
    is_vendor: 'Not Set',
    is_customer_portal: 'Not Set',
    is_prospect: 'Not Set',
    is_deleted: 'Not Set',
  },
}

export default function useGetCustomerAccountInfo(customerId?: string) {
  const initialFilters = {
    page: 1,
    limit: 1,
  }
  const Api = useApi()
  const {filters, onSearch, onSortingChange, onPageChange} = useFilters(initialFilters)

  // For development, return dummy data instead of API call
  const useDummyData = true // Set to false when API is ready

  const {data: response, isFetching} = Api.useGetQuery(
    customerId && !useDummyData ? `/customers/${customerId}/account-info` : '',
    {
      queryId: customerId ? `customer-account-info-${customerId}` : undefined,
      filters,
      isErrorPageRedirection: false,
    },
    {
      enabled: !!customerId && !useDummyData,
    }
  )

  // Use dummy data or parsed API response
  const rawData = useDummyData ? dummyAccountInfoData : response?.data
  const parsedAccountInfo: IAccountInfo = parseCustomerAccountInfo(rawData)

  return {
    accountInfo: parsedAccountInfo,
    isLoading: useDummyData ? false : isFetching,
    filters,
    onSearch,
    onSortingChange,
    onPageChange,
  }
}
