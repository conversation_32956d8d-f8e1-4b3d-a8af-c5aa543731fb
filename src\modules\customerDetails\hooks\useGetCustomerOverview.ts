import useFilters from '../../../hook/useFilters'
import useApi from '../../../services/useApi'
import { parseCustomerOverview } from '../parsings/overview'
import { IOverview } from '../interfaces'

// Dummy data for development
const dummyOverviewData = {
  customer_id: 'CUST-001',
  customer_name: 'Acme Corporation',
  customer_type: 'Business',
  status: 'Active',
  tier: 'Gold',
  industry: 'Technology',
  website: 'https://acme.com',
  total_orders: 25,
  total_revenue: 125000,
  last_order_date: '2024-01-15',
  account_manager: '<PERSON>',
  credit_limit: 50000,
  outstanding_balance: 2850,
  payment_terms: 'Net 30',
  created_date: '2023-06-15',
  last_modified_date: '2024-01-20',
}

export default function useGetCustomerOverview(customerId?: string) {
  const initialFilters = {
    page: 1,
    limit: 1,
  }
  const Api = useApi()
  const {filters, onSearch, onSortingChange, onPageChange} = useFilters(initialFilters)

  // For development, return dummy data instead of API call
  const useDummyData = true // Set to false when API is ready

  const {data: response, isFetching} = Api.useGetQuery(
    customerId && !useDummyData ? `/customers/${customerId}/overview` : '',
    {
      queryId: customerId ? `customer-overview-${customerId}` : undefined,
      filters,
      isErrorPageRedirection: false,
    },
    {
      enabled: !!customerId && !useDummyData,
    }
  )

  // Use dummy data or parsed API response
  const rawData = useDummyData ? dummyOverviewData : response?.data
  const parsedOverview: IOverview = parseCustomerOverview(rawData)

  return {
    overview: parsedOverview,
    isLoading: useDummyData ? false : isFetching,
    filters,
    onSearch,
    onSortingChange,
    onPageChange,
  }
}
