import useApi from '../../../services/useApi'
import {parseCustomerOverview} from '../parsings/overview'
import {useParams} from 'react-router-dom'

export default function useGetCustomerOverview() {
  const {id: customerId} = useParams<{id: string}>()
  const Api = useApi()
  const {data: response, isFetching} = Api.useGetQuery(
    `/customers/bc/${customerId}/overview`,
    {
      queryId: `customer-overview-${customerId}`,
      filters: {
        page: 1,
        limit: 1,
      },
    },
    {
      enabled: !!customerId,
    }
  )

  return {
    overview: parseCustomerOverview(response?.data),
    isLoading: isFetching,
  }
}
