import { IAddress } from '../interfaces'

export const parseCustomerAddresses = (data: any[]): IAddress[] => {
  if (!Array.isArray(data)) return []
  
  return data.map(address => ({
    id: address.id || '',
    type: address.type || '',
    addressLine1: address.address_line_1 || address.addressLine1 || '',
    addressLine2: address.address_line_2 || address.addressLine2 || '',
    city: address.city || '',
    state: address.state || '',
    zipCode: address.zip_code || address.zipCode || '',
    country: address.country || '',
    isDefault: address.is_default || address.isDefault || false,
    isActive: address.is_active || address.isActive || true,
    createdDate: address.created_date || address.createdDate || '',
    lastModifiedDate: address.last_modified_date || address.lastModifiedDate || '',
  }))
}
