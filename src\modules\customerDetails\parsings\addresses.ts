import { IAddress } from '../interfaces'

export const parseCustomerAddresses = (data: any[]): IAddress[] => {
  if (!Array.isArray(data)) return []
  
  return data.map((address) => ({
    id: address.id?.toString() || '',
    customerId: address.customer_id?.toString() || '',
    firstName: address.first_name || '-',
    lastName: address.last_name || '-',
    company: address.company || '-',
    addressLine1: address.street_1 || '-',
    addressLine2: address.street_2 || '-',
    city: address.city || '-',
    state: address.state || '-',
    zipCode: address.zip || '-',
    country: address.country || '-',
    countryIso2: address.country_iso2 || '-',
    phone: address.phone || '-',
    addressType: address.address_type || '-',
    formFields: address.form_fields || [],
  }))
}
