import React from 'react'
import {KTSVG} from '../../../_metronic/helpers'
import {IAccountInfo} from '../interfaces'
import Loading from '../../loading'
import Date from '../../../components/Date/Date'

interface AccountInfoSectionProps {
  accountInfo: IAccountInfo
  isLoading: boolean
}

const InfoField = ({
  icon,
  label,
  value,
  className = '',
  body
}: {
  icon?: string
  label: string
  value?: string
  className?: string
  body?: React.ReactNode
}) => (
  <div className={`d-flex align-items-center mb-3 ${className}`}>
    {icon && (
      <div className='me-3'>
        <KTSVG path={icon} className='svg-icon-5 text-muted' />
      </div>
    )}
    <div className='flex-grow-1'>
      <span className='text-gray-700 fs-6'>{label}:</span>
      {body ? body : <span className='text-gray-800 fw-semibold ms-2'>{value}</span>}
    </div>
  </div>
)

const AccountInfoSection: React.FC<AccountInfoSectionProps> = ({accountInfo, isLoading}) => {
  return (
    <div className='row g-5 mb-5 position-relative'>
      {isLoading && <Loading />}

      {/* General Information */}
      <div className='col-12'>
        <div className='card border'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-5'>
              <KTSVG
                path='/media/icons/duotune/general/gen019.svg'
                className='svg-icon-2 text-primary me-2'
              />
              <h4 className='fw-bold m-0 text-gray-800'>General Information</h4>
            </div>
            <div className='row'>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/communication/com006.svg'
                  label='Account Name'
                  value={accountInfo.generalInformation?.accountName || '-'}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen043.svg'
                  label='Status'
                  value={accountInfo.generalInformation?.status || '-'}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen018.svg'
                  label='Rating'
                  value={accountInfo.generalInformation?.rating || '-'}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen032.svg'
                  label='Federal Tax ID'
                  value={accountInfo.generalInformation?.federalTaxId || '-'}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/general/gen025.svg'
                  label='Account Number'
                  value={accountInfo.generalInformation?.accountNumber || '-'}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen024.svg'
                  label='Tier'
                  value={accountInfo.generalInformation?.tier || '-'}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen016.svg'
                  label='Industry'
                  value={accountInfo.generalInformation?.industry || '-'}
                />
                <InfoField
                  icon='/media/icons/duotune/communication/com011.svg'
                  label='How did you hear about us?'
                  value={accountInfo.generalInformation?.howDidYouHearAboutUs || '-'}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/general/gen005.svg'
                  label='Account Type'
                  value={accountInfo.generalInformation?.accountType || '-'}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen043.svg'
                  label='Customer Priority'
                  value={accountInfo.generalInformation?.customerPriority || '-'}
                />
                <InfoField
                  icon='/media/icons/duotune/communication/com013.svg'
                  label='Website'
                  value={accountInfo.generalInformation?.website || '-'}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Owner & Manager */}
      <div className='col-12'>
        <div className='card border'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-5'>
              <KTSVG
                path='/media/icons/duotune/communication/com014.svg'
                className='svg-icon-2 text-primary me-2'
              />
              <h4 className='fw-bold m-0 text-gray-800'>Owner & Manager</h4>
            </div>
            <div className='row'>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/communication/com006.svg'
                  label='Account Owner'
                  value={accountInfo.ownerManager?.accountOwner || '-'}
                />
                <InfoField
                  icon='/media/icons/duotune/communication/com011.svg'
                  label='Account Manager Email'
                  value={accountInfo.ownerManager?.accountManagerEmail || '-'}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen014.svg'
                  label='Account Manager Assigned Date/Time'
                  body={
                    <Date date={accountInfo.ownerManager?.accountManagerAssignedDateTime} />
                  }
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/communication/com011.svg'
                  label='Account Owner Email'
                  value={accountInfo.ownerManager?.accountOwnerEmail || '-'}
                />
                <InfoField
                  icon='/media/icons/duotune/communication/com005.svg'
                  label='Account Manager Mobile'
                  value={accountInfo.ownerManager?.accountManagerMobile || '-'}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/communication/com006.svg'
                  label='Account Manager'
                  value={accountInfo.ownerManager?.accountManager || '-'}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen016.svg'
                  label='Account Manager Division'
                  value={accountInfo.ownerManager?.accountManagerDivision || '-'}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* BC Information */}
      <div className='col-12'>
        <div className='card border'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-5'>
              <KTSVG
                path='/media/icons/duotune/general/gen016.svg'
                className='svg-icon-2 text-primary me-2'
              />
              <h4 className='fw-bold m-0 text-gray-800'>BC Information</h4>
            </div>
            <div className='row'>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/general/gen005.svg'
                  label='BC Customer Group'
                  value={accountInfo.bcInformation?.bcCustomerGroup || '-'}
                />
                <InfoField
                  icon='/media/icons/duotune/finance/fin010.svg'
                  label='BC Store Credit'
                  value={accountInfo.bcInformation?.bcStoreCredit || '-'}
                />
                <InfoField
                  icon='/media/icons/duotune/communication/com013.svg'
                  label='Cell/Mobile Number'
                  value={accountInfo.bcInformation?.cellMobileNumber || '-'}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/general/gen025.svg'
                  label='BC Group Name'
                  value={accountInfo.bcInformation?.bcGroupName || '-'}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen032.svg'
                  label='BC CustomerId'
                  value={accountInfo.bcInformation?.bcCustomerId || '-'}
                />
                <InfoField
                  icon='/media/icons/duotune/communication/com013.svg'
                  label='SMS Consent'
                  value={accountInfo.bcInformation?.smsConsent || '-'}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='/media/icons/duotune/communication/com011.svg'
                  label='BC Email'
                  value={accountInfo.bcInformation?.bcEmail || '-'}
                />
                <InfoField
                  icon='/media/icons/duotune/general/gen014.svg'
                  label='BC Modified Time'
                  value={accountInfo.bcInformation?.bcModifiedTime || '-'}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Form Fields */}
      <div className='col-12'>
        <div className='card border'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-5'>
              <KTSVG
                path='/media/icons/duotune/general/gen043.svg'
                className='svg-icon-2 text-primary me-2'
              />
              <h4 className='fw-bold m-0 text-gray-800'>Form Fields</h4>
            </div>
            <div className='row'>
              <div className='col-md-6'>
                <InfoField
                  icon='/media/icons/duotune/general/gen043.svg'
                  label='Read & Agree Midwest Terms'
                  value={accountInfo.formFields?.readAgreeMidwestTerms || '-'}
                />
              </div>
              <div className='col-md-6'>
                <InfoField
                  icon='/media/icons/duotune/general/gen005.svg'
                  label='Are you a Business'
                  value={accountInfo.formFields?.areYouABusiness || '-'}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Custom & Other Fields */}
      <div className='col-12'>
        <div className='card border'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-5'>
              <KTSVG
                path='/media/icons/duotune/general/gen032.svg'
                className='svg-icon-2 text-primary me-2'
              />
              <h4 className='fw-bold m-0 text-gray-800'>Custom & Other Fields</h4>
            </div>
            <div className='row'>
              <div className='col-md-4'>
                <InfoField
                  
                  label='Abuser'
                  value={accountInfo.customOtherFields?.abuser || '-'}
                />
                <InfoField
                  
                  label='Is Customer Portal'
                  value={accountInfo.customOtherFields?.isCustomerPortal || '-'}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  
                  label='Is Partner'
                  value={accountInfo.customOtherFields?.isPartner || '-'}
                />
                <InfoField
                  
                  label='Is Prospect'
                  value={accountInfo.customOtherFields?.isProspect || '-'}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  
                  label='Is Vendor'
                  value={accountInfo.customOtherFields?.isVendor || '-'}
                />
                <InfoField
                  
                  label='Is Deleted'
                  value={accountInfo.customOtherFields?.isDeleted || '-'}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AccountInfoSection
