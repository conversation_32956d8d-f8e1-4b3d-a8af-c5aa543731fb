import React from 'react'
import {IAccountInfo} from '../interfaces'
import Loading from '../../loading'
import Date from '../../../components/Date/Date'

interface AccountInfoSectionProps {
  accountInfo: IAccountInfo
  isLoading: boolean
}

const InfoField = ({
  icon,
  label,
  value,
  className = '',
  body,
}: {
  icon?: string
  label: string
  value?: string
  className?: string
  body?: React.ReactNode
}) => (
  <div className={`d-flex align-items-center mb-3 ${className}`}>
    {icon && (
      <div className='me-3'>
        <i className={`${icon} fs-5 text-muted`}></i>
      </div>
    )}
    <div className='d-flex flex-grow-1'>
      <span>{label}:</span>
      {body ? <div className='text-gray-700 ms-2'>{body}</div> : <span className='text-gray-700 ms-2'>{value}</span>}
    </div>
  </div>
)

const AccountInfoSection: React.FC<AccountInfoSectionProps> = ({accountInfo, isLoading}) => {
  return (
    <div className='row g-5 mb-5 position-relative'>
      {isLoading && <Loading />}

      {/* General Information */}
      <div className='col-12'>
        <div className='card border'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-5'>
              <i className='las la-file-alt fs-2 text-primary me-2'></i>
              <h4 className='fw-bold m-0 text-gray-800'>General Information</h4>
            </div>
            <div className='row'>
              <div className='col-md-4'>
                <InfoField
                  icon='las la-building'
                  label='Account Name'
                  value={accountInfo.generalInformation?.accountName || '-'}
                />
                <InfoField
                  icon='las la-check-circle'
                  label='Status'
                  value={accountInfo.generalInformation?.status || ''}
                />
                <InfoField
                  icon='las la-thumbs-up'
                  label='Rating'
                  value={accountInfo.generalInformation?.rating}
                />
                <InfoField
                  icon='las la-hashtag'
                  label='Federal Tax ID'
                  value={accountInfo.generalInformation?.federalTaxId}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='las la-hashtag'
                  label='Account Number'
                  value={accountInfo.generalInformation?.accountNumber}
                />
                <InfoField
                  icon='las la-star'
                  label='Tier'
                  value={accountInfo.generalInformation?.tier || 'Gold'}
                />
                <InfoField
                  icon='las la-industry'
                  label='Industry'
                  value={accountInfo.generalInformation?.industry || 'Technology'}
                />
                <InfoField
                  icon='las la-question-circle'
                  label='How did you hear about us?'
                  value={accountInfo.generalInformation?.howDidYouHearAboutUs}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='las la-tag'
                  label='Account Type'
                  value={accountInfo.generalInformation?.accountType || 'Business'}
                />
                <InfoField
                  icon='las la-exclamation-circle'
                  label='Customer Priority'
                  value={accountInfo.generalInformation?.customerPriority}
                />
                <InfoField
                  icon='las la-globe'
                  label='Website'
                  value={accountInfo.generalInformation?.website || 'https://acme.com'}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Owner & Manager */}
      <div className='col-12'>
        <div className='card border'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-5'>
              <i className='las la-users fs-2 text-primary me-2'></i>
              <h4 className='fw-bold m-0 text-gray-800'>Owner & Manager</h4>
            </div>
            <div className='row'>
              <div className='col-md-4'>
                <InfoField
                  icon='las la-user'
                  label='Account Owner'
                  value={accountInfo.ownerManager?.accountOwner}
                />
                <InfoField
                  icon='las la-envelope'
                  label='Account Manager Email'
                  value={accountInfo.ownerManager?.accountManagerEmail}
                />
                <InfoField
                  icon='las la-calendar'
                  label='Account Manager Assigned Date/Time'
                  body={<Date date={accountInfo.ownerManager?.accountManagerAssignedDateTime} />}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='las la-envelope'
                  label='Account Owner Email'
                  value={accountInfo.ownerManager?.accountOwnerEmail}
                />
                <InfoField
                  icon='las la-phone'
                  label='Account Manager Mobile'
                  value={accountInfo.ownerManager?.accountManagerMobile}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='las la-user-tie'
                  label='Account Manager'
                  value={accountInfo.ownerManager?.accountManager || 'Sarah Johnson'}
                />
                <InfoField
                  icon='las la-building'
                  label='Account Manager Division'
                  value={accountInfo.ownerManager?.accountManagerDivision}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* BC Information */}
      <div className='col-12'>
        <div className='card border'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-5'>
              <i className='las la-globe fs-2 text-primary me-2'></i>
              <h4 className='fw-bold m-0 text-gray-800'>BC Information</h4>
            </div>
            <div className='row'>
              <div className='col-md-4'>
                <InfoField
                  icon='las la-users'
                  label='BC Customer Group'
                  value={accountInfo.bcInformation?.bcCustomerGroup}
                />
                <InfoField
                  icon='las la-dollar-sign'
                  label='BC Store Credit'
                  value={accountInfo.bcInformation?.bcStoreCredit}
                />
                <InfoField
                  icon='las la-phone'
                  label='Cell/Mobile Number'
                  value={accountInfo.bcInformation?.cellMobileNumber}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='las la-tag'
                  label='BC Group Name'
                  value={accountInfo.bcInformation?.bcGroupName}
                />
                <InfoField
                  icon='las la-hashtag'
                  label='BC CustomerId'
                  value={accountInfo.bcInformation?.bcCustomerId}
                />
                <InfoField
                  icon='las la-sms'
                  label='SMS Consent'
                  value={accountInfo.bcInformation?.smsConsent}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  icon='las la-envelope'
                  label='BC Email'
                  value={accountInfo.bcInformation?.bcEmail}
                />
                <InfoField
                  icon='las la-clock'
                  label='BC Modified Time'
                  body={<Date date={accountInfo.bcInformation?.bcModifiedTime} />}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Form Fields */}
      <div className='col-12'>
        <div className='card border'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-5'>
              <i className='las la-file-contract fs-2 text-primary me-2'></i>
              <h4 className='fw-bold m-0 text-gray-800'>Form Fields</h4>
            </div>
            <div className='row'>
              <div className='col-md-6'>
                <InfoField
                  icon='las la-check-square'
                  label='Read & Agree Midwest Terms'
                  value={accountInfo.formFields?.readAgreeMidwestTerms}
                />
              </div>
              <div className='col-md-6'>
                <InfoField
                  icon='las la-suitcase'
                  label='Are you a Business'
                  value={accountInfo.formFields?.areYouABusiness}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Custom & Other Fields */}
      <div className='col-12'>
        <div className='card border'>
          <div className='card-body p-6'>
            <div className='d-flex align-items-center mb-5'>
              <i className='las la-cog fs-2 text-primary me-2'></i>
              <h4 className='fw-bold m-0 text-gray-800'>Custom & Other Fields</h4>
            </div>
            <div className='row'>
              <div className='col-md-4'>
                <InfoField
                  label='Abuser'
                  value={accountInfo.customOtherFields?.abuser}
                />
                <InfoField
                  label='Is Customer Portal'
                  value={accountInfo.customOtherFields?.isCustomerPortal}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  label='Is Partner'
                  value={accountInfo.customOtherFields?.isPartner}
                />
                <InfoField
                  label='Is Prospect'
                  value={accountInfo.customOtherFields?.isProspect}
                />
              </div>
              <div className='col-md-4'>
                <InfoField
                  label='Is Vendor'
                  value={accountInfo.customOtherFields?.isVendor}
                />
                <InfoField
                  label='Is Deleted'
                  value={accountInfo.customOtherFields?.isDeleted}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AccountInfoSection
