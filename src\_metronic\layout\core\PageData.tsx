/* eslint-disable react-hooks/exhaustive-deps */
import React, {FC, ReactNode, createContext, useContext, useEffect, useState, useRef} from 'react'
import {WithChildren} from '../../helpers'

export interface PageLink {
  title: string
  path: string
  isActive: boolean
  isSeparator?: boolean
}

export interface PageDataContextModel {
  pageTitle?: string
  setPageTitle: (_title: string) => void
  pageDescription?: string
  setPageDescription: (_description: string) => void
  pageBreadcrumbs?: Array<PageLink>
  setPageBreadcrumbs: (_breadcrumbs: Array<PageLink>) => void
  pageTitleClassName?: string | null
  setPageTitleClassName: (_className: string | null) => void
  pageContent?: any
  setPageContent: (_content: any) => void
  isLoading?: boolean
  setIsLoading: (_isLoading: boolean) => void
  pageTitleComponent?: ReactNode
  setPageTitleComponent: (_component: ReactNode) => void
}

const PageDataContext = createContext<PageDataContextModel>({
  setPageTitle: (_title: string) => {},
  setPageBreadcrumbs: (_breadcrumbs: Array<PageLink>) => {},
  setPageDescription: (_description: string) => {},
  setPageTitleClassName: (_className: string | null) => {},
  setPageContent: (_content: any) => {},
  setIsLoading: (_isLoading: boolean) => {},
  setPageTitleComponent: (_component: ReactNode) => {},
})

const PageDataProvider: React.FC<WithChildren> = ({children}) => {
  const [pageTitle, setPageTitle] = useState<string>('')
  const [pageDescription, setPageDescription] = useState<string>('')
  const [pageBreadcrumbs, setPageBreadcrumbs] = useState<Array<PageLink>>([])
  const [pageTitleClassName, setPageTitleClassName] = useState<string | null>('')
  const [pageContent, setPageContent] = useState<any>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [pageTitleComponent, setPageTitleComponent] = useState<ReactNode>(null)
  const value: PageDataContextModel = {
    pageTitle,
    setPageTitle,
    pageDescription,
    setPageDescription,
    pageBreadcrumbs,
    setPageBreadcrumbs,
    pageTitleClassName,
    setPageTitleClassName,
    pageContent,
    setPageContent,
    isLoading,
    setIsLoading,
    pageTitleComponent,
    setPageTitleComponent,
  }
  return <PageDataContext.Provider value={value}>{children}</PageDataContext.Provider>
}

function usePageData() {
  return useContext(PageDataContext)
}

type Props = {
  description?: string
  breadcrumbs?: Array<PageLink>
  className?: string
  content?: any
  isLoading?: boolean
  pageTitleComponent?: ReactNode
}

const PageTitle: FC<Props & WithChildren> = ({
  children,
  description,
  breadcrumbs,
  className,
  content,
  isLoading = false,
  pageTitleComponent,
}) => {
  const {
    setPageTitle,
    setPageDescription,
    setPageBreadcrumbs,
    setPageTitleClassName,
    setPageContent,
    setIsLoading,
    setPageTitleComponent,
  } = usePageData()
  const lastPageTitleComponent = useRef<any>(null)
  useEffect(() => {
    if (Array.isArray(children)) {
      setPageTitle(children.join('').toString())
    } else if (children) {
      setPageTitle(children.toString())
    }
    return () => {
      setPageTitle('')
    }
  }, [children])

  useEffect(() => {
    if (description) {
      setPageDescription(description)
    }
    return () => {
      setPageDescription('')
    }
  }, [description])

  useEffect(() => {
    if (breadcrumbs) {
      setPageBreadcrumbs(breadcrumbs)
    }
    return () => {
      setPageBreadcrumbs([])
    }
  }, [breadcrumbs])

  useEffect(() => {
    if (className) {
      setPageTitleClassName(className)
    }
    return () => {
      setPageTitleClassName(null)
    }
  }, [className])

  useEffect(() => {
    if (content) {
      setPageContent(content)
    }
    return () => {
      setPageContent(null)
    }
  }, [content])

  useEffect(() => {
    setIsLoading(isLoading)
  }, [isLoading])

  useEffect(() => {
    if (pageTitleComponent !== lastPageTitleComponent.current) {
      setPageTitleComponent(pageTitleComponent)
      lastPageTitleComponent.current = pageTitleComponent
    }
    return () => {
      setPageTitleComponent(null)
      lastPageTitleComponent.current = null
    }
  }, [pageTitleComponent, setPageTitleComponent])

  return <></>
}

const PageDescription: React.FC<WithChildren> = ({children}) => {
  const {setPageDescription} = usePageData()
  useEffect(() => {
    if (children) {
      setPageDescription(children.toString())
    }
    return () => {
      setPageDescription('')
    }
  }, [children])
  return <></>
}

export {PageDescription, PageTitle, PageDataProvider, usePageData}
