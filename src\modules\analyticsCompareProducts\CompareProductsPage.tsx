import {PageLink} from '../../_metronic/layout/core'
import {Navigation} from '../../components/Navigation'
import useMeta from '../../hook/useMeta'
import AnalyticsCompareProducts from './layouts/AnalyticsCompareProducts'

const breadcrumbs: PageLink[] = [
  {
    title: 'Analytics',
    path: '/analytics',
    isSeparator: false,
    isActive: false,
  },
  {
    title: 'Compare Products',
    path: '/analytics/compare-products',
    isSeparator: false,
    isActive: false,
  },
]

const CompareProductsPage = () => {
  useMeta('Compare Products')

  const navigationData = [
    {
      key: 'compare',
      label: 'Compare',
      component: <AnalyticsCompareProducts />,
    },
  ]

  return (
    <Navigation
      baseUrl='/analytics/compare-products'
      breadcrumbs={breadcrumbs}
      navigationData={navigationData}
      pageTitle='Compare Products'
    />
  )
}

export default CompareProductsPage
