import {PageLink} from '../../_metronic/layout/core'
import {Navigation} from '../../components/Navigation'
import useMeta from '../../hook/useMeta'
import AnalyticsCompareProducts from './layouts/AnalyticsCompareProducts'

const breadcrumbs: PageLink[] = [
  {
    title: 'Analytics',
    path: '/analytics/profitability/profitability',
    isSeparator: false,
    isActive: false,
  },
  {
    title: 'Sold Products',
    path: '/analytics/sold-products',
    isSeparator: false,
    isActive: false,
  },
]

const CompareProductsPage = () => {
  useMeta('Compare Products')

  const navigationData = [
    {
      key: 'compare',
      label: 'Compare',
      component: <AnalyticsCompareProducts />,
    },
  ]

  return (
    <Navigation
      baseUrl='/analytics/sold-products/compare-products'
      breadcrumbs={breadcrumbs}
      navigationData={navigationData}
      pageTitle='Compare Products'
    />
  )
}

export default CompareProductsPage
