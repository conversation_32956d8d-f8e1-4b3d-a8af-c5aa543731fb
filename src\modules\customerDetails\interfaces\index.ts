export interface IOverview {
  customerId: string
  customerName: string
  company: string
  email: string
  phone: string
  customerType: string
  status: string
  tier: string
  tags: string
  website: string
  totalOrders: number
  totalRevenue: string
  totalCount: number
  lastOrderDate: string
  accountManager: string
  creditLimit: string
  creditUsed: string
  outstandingBalance: string
  storeCredit: string
  paymentTerms: string
  createdDate: string
  lastModifiedDate: string
  avgOrderValue: string
  rewardPoints: number
  registrationIpAddress: string
  customerGroupId: number
  notes: string
  taxExemptCategory: string
  acceptsMarketing: boolean
  formFields: Array<{
    name: string
    value: string
  }>
}

export interface IAccountInfo {
  generalInformation: {
    accountName: string
    accountNumber: string
    accountType: string
    status: string
    tier: string
    industry: string
    website: string
    customerPriority: string
    rating: string
    federalTaxId: string
    howDidYouHearAboutUs: string
  }
  ownerManager: {
    accountOwner: string
    accountOwnerEmail: string
    accountManager: string
    accountManagerEmail: string
    accountManagerMobile: string
    accountManagerDivision: string
    accountManagerAssignedDateTime: string
  }
  bcInformation: {
    bcCustomerGroup: string
    bcGroupName: string
    bcEmail: string
    bcStoreCredit: string
    bcCustomerId: string
    bcModifiedTime: string
    cellMobileNumber: string
    smsConsent: string
  }
  formFields: {
    readAgreeMidwestTerms: string
    areYouABusiness: string
  }
  customOtherFields: {
    abuser: string
    isPartner: string
    isVendor: string
    isCustomerPortal: string
    isProspect: string
    isDeleted: string
  }
}

export interface IAddress {
  id: string
  customerId: string
  firstName: string
  lastName: string
  company: string
  addressLine1: string
  addressLine2: string
  city: string
  state: string
  zipCode: string
  country: string
  countryIso2: string
  phone: string
  addressType: string
  formFields: Array<{
    name: string
    value: string
  }>
}

export interface IOrderItem {
  id: string
  productId: string
  productName: string
  sku: string
  quantity: number
  unitPrice: number
  totalPrice: number
}

export interface IOrder {
  id: string
  customerId: string
  orderNumber: string
  orderDate: string
  status: string
  statusId: number
  totalAmount: {
    storefront: {
      total: number
      due: number
      paid: number
    }
    bigcommerce: {
      includingTax: number
      excludingTax: number
    }
  }
  rewards: number
  items: IOrderItem[]
  createdDate: string
  lastModifiedDate: string
}

export interface IFinancialPayment {
  paymentAndCredit: {
    paymentTerm: string
    creditLimit: string
    unpaidBalance: string
    outstandingBalance: string
    paymentTermApprovedBy: string
    paymentTermApprovedDateTime: string
    paymentTermNotes: string
    paymentTermIssues: string
    paymentTermRisk: string
    taxExempt: string
    currency: string
  }
  cashAndCarry: {
    cashAndCarryCheckApproved: string
    checkApprovedBy: string
    checkApprovedDateTime: string
    checkApprovedAmountLimit: string
    notedForCheck: string
    doesTheCustomerHaveAccount: string
  }
  creditCard: {
    customerCreditCardFeesExempt: string
    creditCardFeesExemptApprovedBy: string
    creditCardFeesExemptApprovedDate: string
  }
  ach: {
    achCheckApproved: string
    achCheckApprovedBy: string
    achCheckApprovedDateTime: string
    achFormReceivedAndUploaded: string
    achFormUploadedBy: string
    achFormReceivedUploadedDate: string
  }
  netTerms: {
    netTermReceivedAndUploaded: string
    netTermAgreementUploadedBy: string
    netTermUploadedTime: string
  }
}

export interface IReward {
  id: string
  customerId: string
  balance: number
  earnedUsed: number
  createdAt: string
  description: string
  couponCode: string
  couponId: number
  orderId: number
  minPurchase: number
  rewards: number
  redemption: number
  orderStatus: string | null
  orderTotal: number | null
}

export interface IPricing {
  id: string
  productId: string
  productName: string
  sku: string
  customerPrice: number
  listPrice: number
  discount: number
  discountType: string
  effectiveDate: string
  expirationDate: string
  priceGroup: string
  currency: string
  isActive: boolean
  createdDate: string
  lastModifiedDate: string
}

export interface ISystemActivity {
  systemInformation: {
    attempt1DateCapture: string
    createdBy: string
    lastModifiedBy: string
    userWhoLastModifiedNotes: string
    notesLastModifiedDateTime: string
  }
  activityTimeline: IActivityItem[]
  customerNotes: ICustomerNote[]
}

export interface IActivityItem {
  id: string
  type: 'order' | 'call' | 'email' | 'meeting' | 'note'
  title: string
  description: string
  createdBy: string
  createdDate: string
  status: string
}

export interface ICustomerNote {
  id: string
  author: string
  note: string
  createdDate: string
  isPrivate: boolean
}

export interface IDocument {
  id: string
  documentName: string
  documentType: string
  fileSize: number
  uploadedBy: string
  uploadedDate: string
  lastModifiedDate: string
  downloadUrl: string
  isActive: boolean
  category: string
  description?: string
}
