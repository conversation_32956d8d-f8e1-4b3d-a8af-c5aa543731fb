export interface IOverview {
  customerId: string
  customerName: string
  customerType: string
  status: string
  tier: string
  industry: string
  website: string
  totalOrders: number
  totalRevenue: number
  lastOrderDate: string
  accountManager: string
  creditLimit: number
  outstandingBalance: number
  paymentTerms: string
  createdDate: string
  lastModifiedDate: string
}

export interface IAccountInfo {
  generalInformation: {
    accountName: string
    accountNumber: string
    accountType: string
    status: string
    tier: string
    industry: string
    website: string
    customerPriority: string
    rating: string
    federalTaxId: string
    howDidYouHearAboutUs: string
  }
  ownerManager: {
    accountOwner: string
    accountOwnerEmail: string
    accountManager: string
    accountManagerEmail: string
    accountManagerMobile: string
    accountManagerDivision: string
    accountManagerAssignedDateTime: string
  }
  bcInformation: {
    bcCustomerGroup: string
    bcGroupName: string
    bcEmail: string
    bcStoreCredit: string
    bcCustomerId: string
    bcModifiedTime: string
    cellMobileNumber: string
    smsConsent: string
  }
  formFields: {
    readAgreeMidwestTerms: string
    areYouABusiness: string
  }
  customOtherFields: {
    abuser: string
    isPartner: string
    isVendor: string
    isCustomerPortal: string
    isProspect: string
    isDeleted: string
  }
}

export interface IAddress {
  id: string
  type: string
  addressLine1: string
  addressLine2?: string
  city: string
  state: string
  zipCode: string
  country: string
  isDefault: boolean
  isActive: boolean
  createdDate: string
  lastModifiedDate: string
}

export interface IOrder {
  id: string
  orderNumber: string
  orderDate: string
  status: string
  totalAmount: number
  currency: string
  paymentMethod: string
  shippingAddress: string
  billingAddress: string
  items: IOrderItem[]
  createdDate: string
  lastModifiedDate: string
}

export interface IOrderItem {
  id: string
  productId: string
  productName: string
  sku: string
  quantity: number
  unitPrice: number
  totalPrice: number
}

export interface IFinancialPayment {
  paymentCredit: {
    paymentTerm: string
    creditLimit: string
    unpaidBalance: string
    outstandingBalance: string
    paymentTermApprovedBy: string
    paymentTermApprovedDateTime: string
    paymentTermNotes: string
    paymentTermIssues: string
    paymentTermRisk: string
    taxExempt: string
    currency: string
  }
  cashCarry: {
    cashCarryCheckApproved: string
    checkApprovedBy: string
    checkApprovedDateTime: string
    checkApprovedAmountLimit: string
    notedForCheckCnc: string
    doesCustomerHaveAccountWithCtos: string
  }
  creditCard: {
    customerCreditCardFeesExempt: string
    creditCardFeesExemptApprovedBy: string
    creditCardFeesExemptApprovedDate: string
  }
  ach: {
    achECheckApproved: string
    achCheckApprovedBy: string
    achECheckedApprovedDateTime: string
    achFormReceivedUploaded: string
    achFormUploadedBy: string
    achFormReceivedUploadedDate: string
  }
}

export interface IReward {
  id: string
  rewardType: string
  pointsEarned: number
  pointsRedeemed: number
  pointsBalance: number
  rewardValue: number
  expirationDate: string
  status: string
  createdDate: string
  lastModifiedDate: string
}

export interface IPricing {
  id: string
  productId: string
  productName: string
  sku: string
  customerPrice: number
  listPrice: number
  discount: number
  discountType: string
  effectiveDate: string
  expirationDate: string
  priceGroup: string
  currency: string
  isActive: boolean
  createdDate: string
  lastModifiedDate: string
}

export interface ISystemActivity {
  systemInformation: {
    attempt1DateCapture: string
    createdBy: string
    lastModifiedBy: string
    userWhoLastModifiedNotes: string
    notesLastModifiedDateTime: string
  }
  activityTimeline: IActivityItem[]
  customerNotes: ICustomerNote[]
}

export interface IActivityItem {
  id: string
  type: 'order' | 'call' | 'email' | 'meeting' | 'note'
  title: string
  description: string
  createdBy: string
  createdDate: string
  status: string
}

export interface ICustomerNote {
  id: string
  author: string
  note: string
  createdDate: string
  isPrivate: boolean
}

export interface IDocument {
  id: string
  documentName: string
  documentType: string
  fileSize: number
  uploadedBy: string
  uploadedDate: string
  lastModifiedDate: string
  downloadUrl: string
  isActive: boolean
  category: string
  description?: string
}
