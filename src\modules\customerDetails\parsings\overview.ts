import {IOverview} from '../interfaces'
import {formatPrice} from '../../../utils/common'

export const parseCustomerOverview = (data: any): IOverview => {
  if (!data) return {} as IOverview

  return {
    customerId: data.id?.toString() || '',
    customerName: `${data.first_name || ''} ${data.last_name || ''}`.trim(),
    company: data.company || '-',
    email: data.email || '-',
    phone: data.phone || '-',
    customerType: data.type || '-',
    status: data.status || '',
    tier: data.customer_group_name || '-',
    tags: data.tags || '-',
    website: data.website || '-',
    totalOrders: data.order_count || '-',
    totalRevenue: data.total_spent ? formatPrice(data.total_spent.toString(), false) : '-',
    totalCount: data.total_count || '-',
    lastOrderDate: data.last_activity || '',
    accountManager: data.customer_rep_name || '-',
    creditLimit: data.credit_limit ? formatPrice(data.credit_limit.toString(), false) : '-',
    creditUsed: data.credit_used ? formatPrice(data.credit_used.toString(), false) : '-',
    outstandingBalance: data.outstanding_balance
      ? formatPrice(data.outstanding_balance.toString(), false)
      : '-',
    storeCredit: data.store_credit ? formatPrice(data.store_credit.toString(), false) : '-',
    paymentTerms: data.payment_terms || '',
    createdDate: data.date_created ? new Date(data.date_created * 1000).toISOString() : '',
    lastModifiedDate: data.date_modified ? new Date(data.date_modified * 1000).toISOString() : '',
    avgOrderValue: data.average_order_value || '-',
    rewardPoints: data.reward_points || '-',
    registrationIpAddress: data.registration_ip_address || '-',
    customerGroupId: data.customer_group_id || '-',
    notes: data.notes || '-',
    taxExemptCategory: data.tax_exempt_category || '-',
    acceptsMarketing: data.accepts_marketing || false,
    formFields: data.form_fields || [],
    annualRevenue: data.annual_revenue ? formatPrice(data.annual_revenue.toString(), false) : '-',
    owner: data.owner || '-',
    customerGroupName: data.customer_group_name || '-',
    type: data.type || '-',
    customerRepEmail: data.customer_rep_email || '-',
    customerRepName: data.customer_rep_name || '-',
    lifetimeValue: data.lifetime_value ? formatPrice(data.lifetime_value.toString(), false) : '-',
  }
}
