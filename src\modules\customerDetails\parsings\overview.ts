import {IOverview} from '../interfaces'

export const parseCustomerOverview = (data: any): IOverview => {
  if (!data) return {} as IOverview

  return {
    customerId: data.customer_id || data.customerId || '',
    customerName: data.customer_name || data.customerName || '',
    customerType: data.customer_type || data.customerType || '',
    status: data.status || '',
    tier: data.tier || '',
    industry: data.industry || '',
    website: data.website || '',
    totalOrders: data.total_orders || data.totalOrders || 0,
    totalRevenue: data.total_revenue || data.totalRevenue || 0,
    lastOrderDate: data.last_order_date || data.lastOrderDate || '',
    accountManager: data.account_manager || data.accountManager || '',
    creditLimit: data.credit_limit || data.creditLimit || 0,
    outstandingBalance: data.outstanding_balance || data.outstandingBalance || 0,
    paymentTerms: data.payment_terms || data.paymentTerms || '',
    createdDate: data.created_date || data.createdDate || '',
    lastModifiedDate: data.last_modified_date || data.lastModifiedDate || '',
  }
}
