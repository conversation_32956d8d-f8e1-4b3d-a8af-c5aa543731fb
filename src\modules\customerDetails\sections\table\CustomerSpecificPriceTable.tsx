import {useContext} from 'react'
import {DynamicTable} from '../../../../components/DynamicTable'
import CustomerSpecificPriceTableRow from './CustomerSpecificPriceTableRow'
import {useAppSelector} from '../../../../redux/useTypedSelector'
import { CustomerSpecificPriceContext } from '../../context'
import OverlayModalCustomerSpecificPrice from '../actions-menu/OverlayModalCustomerSpecificPrice'

const CustomerSpecificPriceTable = () => {
  const {CustomerSpecificPriceData, isLoading, filters, onSortingChange, setCheckedRows} =
    useContext(CustomerSpecificPriceContext)
  const {user} = useAppSelector((state) => state.auth)

  const columns = [
    {
      key: 'product_id',
      label: 'Product ID',
      isSorted: true,
      headerStyle: 'min-w-150px',
    },
    {
      key: 'product_name',
      label: 'Product Name',
      isSorted: true,
      headerStyle: 'min-w-400px',
    },
    {
      key: 'sku',
      label: 'Product SKU',
      isSorted: true,
      headerStyle: 'min-w-175px',
    },
    ...(user?.is_owner
      ? [
          {
            key: 'cost',
            label: 'Cost',
            isSorted: false,
            headerStyle: 'min-w-120px',
          },
        ]
      : []),
    {
      key: 'price',
      label: 'Price',
      isSorted: true,
      headerStyle: 'min-w-120px',
      style: ' justify-content-center',
    },
    {
      key: 'distributor',
      label: 'Distributor',
      isSorted: false,
      headerStyle: 'min-w-150px',
    },
    {
      key: 'vip',
      label: 'Vip',
      isSorted: false,
      headerStyle: 'min-w-150px',
    },
    {
      key: 'updated_by',
      label: 'Last Updated By',
      isSorted: true,
      headerStyle: 'min-w-175px',
    },
    {
      key: 'updated_at',
      label: 'Last Update Date',
      isSorted: true,
      headerStyle: 'min-w-175px',
    },
    {
      key: 'action',
      label: 'Action',
      isSorted: false,
      headerStyle: 'w-80px text-center',
      render: (row: any) => <OverlayModalCustomerSpecificPrice row={row} />,
    },
  ]

  return (
    <div>
      <DynamicTable
        id='customer-specific-price-table'
        data={CustomerSpecificPriceData}
        filters={filters}
        sortableColumns={columns}
        onSortingChange={onSortingChange}
        loading={isLoading}
        TableRow={CustomerSpecificPriceTableRow}
        checkedRowId={'id'}
        setCheckedRows={setCheckedRows}
        hasPermission={user?.is_owner}
      />
    </div>
  )
}

export default CustomerSpecificPriceTable
