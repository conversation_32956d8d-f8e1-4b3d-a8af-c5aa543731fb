import {createContext} from 'react'

export const OverviewContext = createContext({
  overview: {} as any,
  isLoading: false,
  updateCustomerTags: (payload: any, customerId: any) => {},
  isOperationLoading: false,
})

export const AccountInfoContext = createContext({
  accountInfo: {} as any,
  isLoading: false,
})

export const AddressesContext = createContext({
  addresses: [] as any,
  isLoading: false,
})

export const OrdersContext = createContext({
  orders: [] as any,
  isLoading: false,
  onSortingChange: (key: string, value: string) => {},
  filters: {},
})

export const FinancialPaymentContext = createContext({
  financialPayment: {} as any,
  isLoading: false,
})

export const RewardContext = createContext({
  rewards: [] as any,
  isLoading: false,
  onSortingChange: (key: string, value: string) => {},
  filters: {},
})

export const SystemActivityContext = createContext({
  systemActivity: {} as any,
  isLoading: false,
})

export const DocumentsContext = createContext({
  documents: [] as any,
  isLoading: false,
})

export const CustomerSpecificPriceContext = createContext({
  customerId: null as any,
  CustomerSpecificPriceData: [],
  isLoading: false,
  onSortingChange: (key: string, value: string) => {},
  filters: {},
  onSearch: (searchValue: string) => {},
  CustomerSpecificPriceMeta: {},
  refetch: () => {},
  checkedRows: [] as any,
  setCheckedRows: (checkedRows: any) => {},
  onAddCustomerSpecificPrice: (data: any, setError: Function) => {},
  onDeleteCustomerSpecificPrice: (data: any) => {},
  onUpdateCustomerSpecificPrice: (product_id: any, price: any, id: any) => {},
  isOperationLoading: false,
  refetchFilter: () => {},
  products: [] as any,
  productPagination: {},
  isLoadingProducts: false,
  onProductPageChange: (page: number) => {},
  onProductSearch: (searchValue: string) => {},
})
