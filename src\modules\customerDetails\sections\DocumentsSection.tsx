import React, {useContext, useState} from 'react'
import {DocumentsContext} from '../context'
import {useAppSelector} from '../../../redux/useTypedSelector'
import {getFileIconClass, toUnderscoreName} from '../utils'
import Loading from '../../loading'

const DocumentsSection = () => {
  const {user} = useAppSelector((state) => state.auth)
  const {documents, isLoading} = useContext(DocumentsContext)
  const [hoverStates, setHoverStates] = useState<boolean[]>(new Array(documents.length).fill(false))

  const handleDownload = async (image: any) => {
    const fileName = toUnderscoreName(image.title|| 'file')
    const fileUrl = `${process.env.REACT_APP_IMAGE_URL}/admin/api/customers/salesforce-documents/download?version_id=${image?.version_id}`

    const response = await fetch(fileUrl, {
      headers: {
        'X-AUTH-TOKEN': user?.access_token,
        'X-STORE-ID': '63da3e98b702e324567f76f9',
        'X-TENANT-ID': user?.tenant_id,
      },
    })

    const blob = await response.blob()
    const blobUrl = window.URL.createObjectURL(blob)
    const extension = blob?.type?.split('/')?.[1] || ''
    const link = document.createElement('a')
    link.href = blobUrl
    link.download = `${fileName}.${extension}`  
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(blobUrl)
  }

  const handleMouseEnter = (index: number) => {
    setHoverStates((prev) => {
      const newStates = [...prev]
      newStates[index] = true
      return newStates
    })
  }

  const handleMouseLeave = (index: number) => {
    setHoverStates((prev) => {
      const newStates = [...prev]
      newStates[index] = false
      return newStates
    })
  }

  return (
    <div className='card card-flush border position-relative'>
      {isLoading && <Loading />}
      <div className='card-body'>
        <div className='d-flex flex-wrap gap-5'>
          {documents.map((doc: any, idx: any) => (
            <div
              key={idx}
              className='file-item'
              onMouseEnter={() => handleMouseEnter(idx)}
              onMouseLeave={() => handleMouseLeave(idx)}
              style={{position: 'relative'}}
            >
              <div className='hover-group rounded border position-relative'>
                {hoverStates[idx] && (
                  <div className='d-flex justify-content-end align-items-center position-absolute w-100 top-0 end-0 z-index-2'>
                    <div className='d-flex w-100 p-3 gap-3 justify-content-end'>
                      <button
                        className='btn btn-sm btn-icon btn-light-primary w-30px h-30px'
                        onClick={() => handleDownload(doc)}
                        title='Download'
                      >
                        <i className='bi bi-cloud-arrow-down fs-3'></i>
                      </button>
                    </div>
                  </div>
                )}
                {doc.file_type === 'attachment' ? (
                  <div className='w-150px h-125px p-3 position-relative'>
                    <div className='w-100 h-80px text-center overflow-hidden'>
                      <img
                        className='object-fit-cover w-100'
                        src={`https://via.placeholder.com/150x80?text=${encodeURIComponent(
                          doc.title
                        )}`}
                        alt={doc.title}
                        title={doc.title}
                      />
                    </div>
                  </div>
                ) : (
                  <div className='w-150px h-125px p-4 d-flex flex-column justify-content-between'>
                    <div>
                      <i className={`${getFileIconClass(doc.title.split('.').pop())} fs-3x`}></i>
                    </div>
                    <div>
                      <p className='fw-semibold text-truncate w-100 mb-2 lh-1' title={doc.title}>
                        {doc.title}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default DocumentsSection
