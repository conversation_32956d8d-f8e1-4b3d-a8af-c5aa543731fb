import useFilters from '../../../hook/useFilters'
import useApi from '../../../services/useApi'

const useGetProductsDropdown = () => {
  const Api = useApi()

  const initialFilters = {
    page: 1,
    limit: 20,
    search: '',
    classification_filter: '',
    user_filter: '',
    supplier_filter: '',
    hide_products: true,
  }

  const {filters, onSearch, onPageChange} = useFilters(initialFilters)

  const {data: response, isFetching} = Api.useGetQuery(
    `/products/price-list/filtered-products/dropdown`,
    {
      filters: filters,
    }
  )

  return {
    products: response?.data || [],
    pagination: response?.['meta'] || {},
    isLoading: isFetching,
    onPageChange,
    onSearch,
  }
}

export default useGetProductsDropdown
