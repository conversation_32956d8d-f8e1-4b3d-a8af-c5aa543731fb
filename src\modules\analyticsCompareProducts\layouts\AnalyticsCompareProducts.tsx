import {useState, useEffect} from 'react'
import AnalyticsCompareProductsHeader from '../sections/headers/AnalyticsCompareProductsHeader'
import AnalyticsCompareProductsTable from '../sections/tables/AnalyticsCompareProductsTable'
import {AnalyticsCompareProductsContext} from '../context'
import useAnalyticsCompareProducts from '../hooks/useAnalyticsCompareProducts'

const AnalyticsCompareProducts = () => {
  const [checkedRows, setCheckedRows] = useState<string[]>([])
  const {products, isLoading, filters, onSearch, onSortingChange, meta, onDateChange} = useAnalyticsCompareProducts()

  useEffect(() => {
    setCheckedRows([])
  }, [isLoading])

  const contextValue = {
    checkedRows,
    setCheckedRows,
    products,
    isLoading,
    filters,
    onSearch,
    onSortingChange,
    meta,
    onDateChange,
  }

  return (
    <AnalyticsCompareProductsContext.Provider value={contextValue}>
      <AnalyticsCompareProductsHeader />
      <AnalyticsCompareProductsTable />
    </AnalyticsCompareProductsContext.Provider>
  )
}

export default AnalyticsCompareProducts
