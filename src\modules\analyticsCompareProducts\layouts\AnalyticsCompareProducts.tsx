import {useState, useEffect} from 'react'
import AnalyticsCompareProductsHeader from '../sections/headers/AnalyticsCompareProductsHeader'
import AnalyticsCompareProductsTable from '../sections/tables/AnalyticsCompareProductsTable'
import {AnalyticsCompareProductsContext} from '../context'
import useAnalyticsCompareProducts from '../hooks/useAnalyticsCompareProducts'
import {Pagination} from '../../../utils/pagination'

const AnalyticsCompareProducts = () => {
  const [checkedRows, setCheckedRows] = useState<string[]>([])
  const {products, isLoading, filters, onSearch, onSortingChange, onPageChange, pagination, meta} =
    useAnalyticsCompareProducts()

  useEffect(() => {
    setCheckedRows([])
  }, [isLoading])

  const contextValue = {
    checkedRows,
    setCheckedRows,
    products,
    isLoading,
    filters,
    onSearch,
    onSortingChange,
    onPageChange,
    pagination,
    meta,
  }

  return (
    <AnalyticsCompareProductsContext.Provider value={contextValue}>
      <AnalyticsCompareProductsHeader />
      <AnalyticsCompareProductsTable />
      <Pagination pagination={pagination} onPageChange={onPageChange} />
    </AnalyticsCompareProductsContext.Provider>
  )
}

export default AnalyticsCompareProducts
